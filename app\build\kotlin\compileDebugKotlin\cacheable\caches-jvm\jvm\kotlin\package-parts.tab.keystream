   # c o m / c r a f t w o r k s / m u s i c / M a i n A c t i v i t y K t    c o m / c r a f t w o r k s / m u s i c / N a v G r a p h K t   ! c o m / c r a f t w o r k s / m u s i c / d a t a / A l b u m K t   " c o m / c r a f t w o r k s / m u s i c / d a t a / A r t i s t K t   ! c o m / c r a f t w o r k s / m u s i c / d a t a / L y r i c K t   $ c o m / c r a f t w o r k s / m u s i c / d a t a / P l a y l i s t K t   ! c o m / c r a f t w o r k s / m u s i c / d a t a / R a d i o K t     c o m / c r a f t w o r k s / m u s i c / d a t a / S o n g K t   - c o m / c r a f t w o r k s / m u s i c / l y r i c s / G e t L R C L I B L y r i c s K t   / c o m / c r a f t w o r k s / m u s i c / m a n a g e r s / S e t t i n g s M a n a g e r K t   - c o m / c r a f t w o r k s / m u s i c / p l a y e r / M e d i a C o n t r o l l e r K t   ' c o m / c r a f t w o r k s / m u s i c / p r o v i d e r s / C o m m o n K t   9 c o m / c r a f t w o r k s / m u s i c / p r o v i d e r s / l o c a l / L o c a l P l a y l i s t I m a g e K t   A c o m / c r a f t w o r k s / m u s i c / p r o v i d e r s / n a v i d r o m e / D o w n l o a d N a v i d r o m e S o n g s K t   = c o m / c r a f t w o r k s / m u s i c / p r o v i d e r s / n a v i d r o m e / G e t N a v i d r o m e A l b u m s K t   > c o m / c r a f t w o r k s / m u s i c / p r o v i d e r s / n a v i d r o m e / G e t N a v i d r o m e A r t i s t s K t   A c o m / c r a f t w o r k s / m u s i c / p r o v i d e r s / n a v i d r o m e / G e t N a v i d r o m e F a v o u r i t e s K t   = c o m / c r a f t w o r k s / m u s i c / p r o v i d e r s / n a v i d r o m e / G e t N a v i d r o m e L y r i c s K t   @ c o m / c r a f t w o r k s / m u s i c / p r o v i d e r s / n a v i d r o m e / G e t N a v i d r o m e P l a y l i s t s K t   = c o m / c r a f t w o r k s / m u s i c / p r o v i d e r s / n a v i d r o m e / G e t N a v i d r o m e R a d i o s K t   < c o m / c r a f t w o r k s / m u s i c / p r o v i d e r s / n a v i d r o m e / G e t N a v i d r o m e S o n g s K t   = c o m / c r a f t w o r k s / m u s i c / p r o v i d e r s / n a v i d r o m e / G e t N a v i d r o m e S t a t u s K t   @ c o m / c r a f t w o r k s / m u s i c / p r o v i d e r s / n a v i d r o m e / M a r k N a v i d r o m e A s P l a y e d K t   > c o m / c r a f t w o r k s / m u s i c / p r o v i d e r s / n a v i d r o m e / N a v i d r o m e C o n n e c t i o n K t   > c o m / c r a f t w o r k s / m u s i c / p r o v i d e r s / n a v i d r o m e / S e t N a v i d r o m e S t a r r e d K t   , c o m / c r a f t w o r k s / m u s i c / u i / e l e m e n t s / A l b u m C a r d K t   - c o m / c r a f t w o r k s / m u s i c / u i / e l e m e n t s / A r t i s t C a r d K t   2 c o m / c r a f t w o r k s / m u s i c / u i / e l e m e n t s / B u t t o n A n i m a t i o n K t   8 c o m / c r a f t w o r k s / m u s i c / u i / e l e m e n t s / C o n n e c t i o n E r r o r D i a l o g K t   , c o m / c r a f t w o r k s / m u s i c / u i / e l e m e n t s / G e n r e P i l l K t   / c o m / c r a f t w o r k s / m u s i c / u i / e l e m e n t s / P l a y l i s t C a r d K t   / c o m / c r a f t w o r k s / m u s i c / u i / e l e m e n t s / P r o v i d e r C a r d K t   , c o m / c r a f t w o r k s / m u s i c / u i / e l e m e n t s / R a d i o C a r d K t   + c o m / c r a f t w o r k s / m u s i c / u i / e l e m e n t s / S o n g C a r d K t   0 c o m / c r a f t w o r k s / m u s i c / u i / e l e m e n t s / S o n g L a z y L i s t s K t   / c o m / c r a f t w o r k s / m u s i c / u i / e l e m e n t s / V e r t i c a l T e x t K t   < c o m / c r a f t w o r k s / m u s i c / u i / e l e m e n t s / d i a l o g s / A p p e a r a n c e D i a l o g s K t   : c o m / c r a f t w o r k s / m u s i c / u i / e l e m e n t s / d i a l o g s / D o w n l o a d D i a l o g s K t   : c o m / c r a f t w o r k s / m u s i c / u i / e l e m e n t s / d i a l o g s / P l a y b a c k D i a l o g s K t   : c o m / c r a f t w o r k s / m u s i c / u i / e l e m e n t s / d i a l o g s / P l a y l i s t D i a l o g s K t   : c o m / c r a f t w o r k s / m u s i c / u i / e l e m e n t s / d i a l o g s / P r o v i d e r D i a l o g s K t   7 c o m / c r a f t w o r k s / m u s i c / u i / e l e m e n t s / d i a l o g s / R a d i o D i a l o g s K t   6 c o m / c r a f t w o r k s / m u s i c / u i / e l e m e n t s / d i a l o g s / S o r t D i a l o g s K t   : c o m / c r a f t w o r k s / m u s i c / u i / e l e m e n t s / d i a l o g s / D i a l o g F o c u s a b l e K t   , c o m / c r a f t w o r k s / m u s i c / u i / p l a y i n g / N o w P l a y i n g K t   6 c o m / c r a f t w o r k s / m u s i c / u i / p l a y i n g / N o w P l a y i n g B a c k g r o u n d K t   4 c o m / c r a f t w o r k s / m u s i c / u i / p l a y i n g / N o w P l a y i n g E l e m e n t s K t   5 c o m / c r a f t w o r k s / m u s i c / u i / p l a y i n g / N o w P l a y i n g L a n d s c a p e K t   2 c o m / c r a f t w o r k s / m u s i c / u i / p l a y i n g / N o w P l a y i n g L y r i c s K t   6 c o m / c r a f t w o r k s / m u s i c / u i / p l a y i n g / N o w P l a y i n g M i n i P l a y e r K t   4 c o m / c r a f t w o r k s / m u s i c / u i / p l a y i n g / N o w P l a y i n g P o r t r a i t K t   4 c o m / c r a f t w o r k s / m u s i c / u i / s c r e e n s / A l b u m D e t a i l s S c r e e n K t   - c o m / c r a f t w o r k s / m u s i c / u i / s c r e e n s / A l b u m S c r e e n K t   5 c o m / c r a f t w o r k s / m u s i c / u i / s c r e e n s / A r t i s t D e t a i l s S c r e e n K t   / c o m / c r a f t w o r k s / m u s i c / u i / s c r e e n s / A r t i s t s S c r e e n K t   , c o m / c r a f t w o r k s / m u s i c / u i / s c r e e n s / H o m e S c r e e n K t   7 c o m / c r a f t w o r k s / m u s i c / u i / s c r e e n s / P l a y l i s t D e t a i l s S c r e e n K t   0 c o m / c r a f t w o r k s / m u s i c / u i / s c r e e n s / P l a y l i s t S c r e e n K t   - c o m / c r a f t w o r k s / m u s i c / u i / s c r e e n s / R a d i o S c r e e n K t   / c o m / c r a f t w o r k s / m u s i c / u i / s c r e e n s / S e t t i n g S c r e e n K t   - c o m / c r a f t w o r k s / m u s i c / u i / s c r e e n s / S o n g s S c r e e n K t   = c o m / c r a f t w o r k s / m u s i c / u i / s c r e e n s / s e t t i n g s / S e t t i n g s A p p e a r a n c e K t   ; c o m / c r a f t w o r k s / m u s i c / u i / s c r e e n s / s e t t i n g s / S e t t i n g s P l a y b a c k K t   < c o m / c r a f t w o r k s / m u s i c / u i / s c r e e n s / s e t t i n g s / S e t t i n g s P r o v i d e r s K t   % c o m / c r a f t w o r k s / m u s i c / u i / t h e m e / C o l o r K t   % c o m / c r a f t w o r k s / m u s i c / u i / t h e m e / T h e m e K t   $ c o m / c r a f t w o r k s / m u s i c / u i / t h e m e / T y p e K t                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    