{"logs": [{"outputFile": "com.craftworks.music.app-mergeDebugResources-3:/values-pt/values-pt.xml", "map": [{"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\c3709e4bfd5d4e5c1ed8a624d490cd12\\transformed\\material-release\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "88", "endOffsets": "139"}, "to": {"startLines": "131", "startColumns": "4", "startOffsets": "13159", "endColumns": "88", "endOffsets": "13243"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\114a5d5301d7649ab21683cbabeecc89\\transformed\\media3-exoplayer-1.7.1\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,195,267,333,410,477,578,671", "endColumns": "69,69,71,65,76,66,100,92,69", "endOffsets": "120,190,262,328,405,472,573,666,736"}, "to": {"startLines": "61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "5822,5892,5962,6034,6100,6177,6244,6345,6438", "endColumns": "69,69,71,65,76,66,100,92,69", "endOffsets": "5887,5957,6029,6095,6172,6239,6340,6433,6503"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\2fbbcb27eaccaf382f754b7d51fec492\\transformed\\mediarouter-1.7.0\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,183,282,387,494,605,731,845,949,1040,1215,1375,1542,1706,1862,2032,2196,2319,2415,2520,2617,2713,2808,2899,3018,3134,3218,3306,3389,3491,3607,3700,3810,3921,4008", "endColumns": "127,98,104,106,110,125,113,103,90,174,159,166,163,155,169,163,122,95,104,96,95,94,90,118,115,83,87,82,101,115,92,109,110,86,98", "endOffsets": "178,277,382,489,600,726,840,944,1035,1210,1370,1537,1701,1857,2027,2191,2314,2410,2515,2612,2708,2803,2894,3013,3129,3213,3301,3384,3486,3602,3695,3805,3916,4003,4102"}, "to": {"startLines": "71,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6595,13727,13826,13931,14038,14149,14275,14389,14493,14584,14759,14919,15086,15250,15406,15576,15740,15863,15959,16064,16161,16257,16352,16443,16562,16678,16762,16850,16933,17035,17151,17244,17354,17465,17552", "endColumns": "127,98,104,106,110,125,113,103,90,174,159,166,163,155,169,163,122,95,104,96,95,94,90,118,115,83,87,82,101,115,92,109,110,86,98", "endOffsets": "6718,13821,13926,14033,14144,14270,14384,14488,14579,14754,14914,15081,15245,15401,15571,15735,15858,15954,16059,16156,16252,16347,16438,16557,16673,16757,16845,16928,17030,17146,17239,17349,17460,17547,17646"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\4f725fd807947c49350c5a696b32f0e9\\transformed\\preference-1.2.1\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,262,341,492,661,748", "endColumns": "69,86,78,150,168,86,80", "endOffsets": "170,257,336,487,656,743,824"}, "to": {"startLines": "39,70,174,175,185,191,192", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3853,6508,17829,17908,18812,19405,19492", "endColumns": "69,86,78,150,168,86,80", "endOffsets": "3918,6590,17903,18054,18976,19487,19568"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\c9938815eb16e1bbe389a6512658438a\\transformed\\material3-release\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,295,411,527,629,726,840,974,1092,1244,1328,1429,1524,1624,1739,1869,1975,2114,2250,2381,2547,2674,2794,2918,3038,3134,3231,3351,3467,3567,3678,3787,3927,4072,4182,4285,4371,4465,4557,4673,4763,4852,4953,5033,5117,5218,5324,5416,5515,5603,5715,5816,5920,6039,6119,6219", "endColumns": "118,120,115,115,101,96,113,133,117,151,83,100,94,99,114,129,105,138,135,130,165,126,119,123,119,95,96,119,115,99,110,108,139,144,109,102,85,93,91,115,89,88,100,79,83,100,105,91,98,87,111,100,103,118,79,99,91", "endOffsets": "169,290,406,522,624,721,835,969,1087,1239,1323,1424,1519,1619,1734,1864,1970,2109,2245,2376,2542,2669,2789,2913,3033,3129,3226,3346,3462,3562,3673,3782,3922,4067,4177,4280,4366,4460,4552,4668,4758,4847,4948,5028,5112,5213,5319,5411,5510,5598,5710,5811,5915,6034,6114,6214,6306"}, "to": {"startLines": "74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6903,7022,7143,7259,7375,7477,7574,7688,7822,7940,8092,8176,8277,8372,8472,8587,8717,8823,8962,9098,9229,9395,9522,9642,9766,9886,9982,10079,10199,10315,10415,10526,10635,10775,10920,11030,11133,11219,11313,11405,11521,11611,11700,11801,11881,11965,12066,12172,12264,12363,12451,12563,12664,12768,12887,12967,13067", "endColumns": "118,120,115,115,101,96,113,133,117,151,83,100,94,99,114,129,105,138,135,130,165,126,119,123,119,95,96,119,115,99,110,108,139,144,109,102,85,93,91,115,89,88,100,79,83,100,105,91,98,87,111,100,103,118,79,99,91", "endOffsets": "7017,7138,7254,7370,7472,7569,7683,7817,7935,8087,8171,8272,8367,8467,8582,8712,8818,8957,9093,9224,9390,9517,9637,9761,9881,9977,10074,10194,10310,10410,10521,10630,10770,10915,11025,11128,11214,11308,11400,11516,11606,11695,11796,11876,11960,12061,12167,12259,12358,12446,12558,12659,12763,12882,12962,13062,13154"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\14cf378553861cbecadb791c6e2341a9\\transformed\\core-1.16.0\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,563,673,793", "endColumns": "96,101,98,99,109,109,119,100", "endOffsets": "147,249,348,448,558,668,788,889"}, "to": {"startLines": "30,31,32,33,34,35,36,184", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2934,3031,3133,3232,3332,3442,3552,18711", "endColumns": "96,101,98,99,109,109,119,100", "endOffsets": "3026,3128,3227,3327,3437,3547,3667,18807"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\64a01e76fd68fe16459fb0e17d50396b\\transformed\\appcompat-1.6.1\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,1850,1935,2042,2153,2255,2363,2471,2581,2743,2843", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "220,326,433,522,623,742,827,907,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,1930,2037,2148,2250,2358,2466,2576,2738,2838,2924"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,178", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,1850,1935,2042,2153,2255,2363,2471,2581,2743,18234", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "220,326,433,522,623,742,827,907,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,1930,2037,2148,2250,2358,2466,2576,2738,2838,18315"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\886fef481c8ce4a2616ccf22a8cd1328\\transformed\\foundation-release\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,146,229", "endColumns": "90,82,84", "endOffsets": "141,224,309"}, "to": {"startLines": "29,189,190", "startColumns": "4,4,4", "startOffsets": "2843,19237,19320", "endColumns": "90,82,84", "endOffsets": "2929,19315,19400"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\b12f5ec7860c79fe319ea01d980e4aac\\transformed\\ui-release\\res\\values-pt\\values-pt.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "197,292,378,475,574,660,743,840,931,1018,1103,1193,1269,1354,1430,1509,1584,1660,1727", "endColumns": "94,85,96,98,85,82,96,90,86,84,89,75,84,75,78,74,75,66,112", "endOffsets": "287,373,470,569,655,738,835,926,1013,1098,1188,1264,1349,1425,1504,1579,1655,1722,1835"}, "to": {"startLines": "37,38,40,42,43,72,73,172,173,176,177,179,180,181,182,183,186,187,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3672,3767,3923,4096,4195,6723,6806,17651,17742,18059,18144,18320,18396,18481,18557,18636,18981,19057,19124", "endColumns": "94,85,96,98,85,82,96,90,86,84,89,75,84,75,78,74,75,66,112", "endOffsets": "3762,3848,4015,4190,4276,6801,6898,17737,17824,18139,18229,18391,18476,18552,18631,18706,19052,19119,19232"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\493af54b1b0004569d1870cf1ba0af14\\transformed\\media3-session-1.7.1\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,226,306,403,499,584,667,753,862,955,1022,1130,1225,1324,1398,1497,1578,1672,1741,1808,1885,1962,2054", "endColumns": "75,94,79,96,95,84,82,85,108,92,66,107,94,98,73,98,80,93,68,66,76,76,91,96", "endOffsets": "126,221,301,398,494,579,662,748,857,950,1017,1125,1220,1319,1393,1492,1573,1667,1736,1803,1880,1957,2049,2146"}, "to": {"startLines": "41,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,132,133,134,135,136,137", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4020,4281,4376,4456,4553,4649,4734,4817,4903,5012,5105,5172,5280,5375,5474,5548,5647,5728,13248,13317,13384,13461,13538,13630", "endColumns": "75,94,79,96,95,84,82,85,108,92,66,107,94,98,73,98,80,93,68,66,76,76,91,96", "endOffsets": "4091,4371,4451,4548,4644,4729,4812,4898,5007,5100,5167,5275,5370,5469,5543,5642,5723,5817,13312,13379,13456,13533,13625,13722"}}]}]}