package com.craftworks.music.data

import android.net.Uri
import android.os.Bundle
import androidx.annotation.OptIn
import androidx.compose.runtime.mutableStateListOf
import androidx.core.net.toUri
import androidx.media3.common.MediaItem
import androidx.media3.common.MediaMetadata
import androidx.media3.common.util.UnstableApi

var playlistList:MutableList<MediaData.Playlist> = mutableStateListOf()

data class Playlist (
    val name: String,
    var coverArt: Uri,
    var songs: List<MediaData.Song> = emptyList(),
    val navidromeID: String? = ""
)

@OptIn(UnstableApi::class)
fun MediaData.Playlist.toMediaItem(): MediaItem {
    val mediaMetadata = MediaMetadata.Builder()
        .setTitle(<EMAIL>)
        .setDescription(<EMAIL>)
        .setArtworkUri(<EMAIL>?.toUri())
        .setArtworkData(<EMAIL>?.toByteArray(), MediaMetadata.PICTURE_TYPE_OTHER)
        .setIsBrowsable(true)
        .setIsPlayable(false)
        .setMediaType(MediaMetadata.MEDIA_TYPE_PLAYLIST)
        .setDurationMs(<EMAIL>())
        .setExtras(Bundle().apply {
            putString("navidromeID", <EMAIL>)
        })
        .build()

    return MediaItem.Builder()
        .setMediaId(<EMAIL>())
        .setMediaMetadata(mediaMetadata)
        .build()
}