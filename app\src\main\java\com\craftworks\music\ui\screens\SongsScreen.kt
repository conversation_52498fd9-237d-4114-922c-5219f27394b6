package com.craftworks.music.ui.screens

import android.content.res.Configuration
import android.util.Log
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.expandVertically
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.Search
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.material3.pulltorefresh.PullToRefreshBox
import androidx.compose.material3.pulltorefresh.rememberPullToRefreshState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.media3.common.util.UnstableApi
import androidx.media3.session.MediaController
import com.craftworks.music.R
import com.craftworks.music.player.SongHelper
import com.craftworks.music.ui.elements.HorizontalLineWithNavidromeCheck
import com.craftworks.music.ui.elements.SongsHorizontalColumn
import com.craftworks.music.ui.elements.dialogs.AddSongToPlaylist
import com.craftworks.music.ui.elements.dialogs.showAddSongToPlaylistDialog
import com.craftworks.music.ui.viewmodels.SongsScreenViewModel
import kotlinx.coroutines.launch

@androidx.annotation.OptIn(UnstableApi::class)
@OptIn(ExperimentalMaterial3Api::class)
@Preview(showBackground = true, showSystemUi = true)
@Composable
fun SongsScreen(
    mediaController: MediaController? = null,
    viewModel: SongsScreenViewModel = viewModel()
) {
    val leftPadding = if (LocalConfiguration.current.orientation != Configuration.ORIENTATION_LANDSCAPE) 0.dp else 80.dp
    val context = LocalContext.current

    var isSearchFieldOpen by remember { mutableStateOf(false) }
    var searchFilter by remember { mutableStateOf("") }

    val allSongsList by viewModel.allSongs.collectAsStateWithLifecycle()
    val isLoading by viewModel.isLoading.collectAsStateWithLifecycle()

    // Load data when screen is first displayed
    LaunchedEffect(Unit) {
        viewModel.loadDataIfNeeded()
    }

    val coroutineScope = rememberCoroutineScope()

    val state = rememberPullToRefreshState()
    var isRefreshing by remember { mutableStateOf(false) }

    val onRefresh: () -> Unit = {
        isRefreshing = true
        viewModel.reloadData()
        isRefreshing = false
    }

    PullToRefreshBox(
        state = state,
        isRefreshing = isRefreshing,
        onRefresh = onRefresh
    ) {
        Column(modifier = Modifier
            .fillMaxWidth()
            .padding(
                start = leftPadding,
                top = WindowInsets.statusBars.asPaddingValues().calculateTopPadding()
            )) {
            Row(verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.padding(horizontal = 12.dp)) {
                Icon(
                    imageVector = ImageVector.vectorResource(R.drawable.round_music_note_24),
                    contentDescription = "Songs Icon",
                    tint = MaterialTheme.colorScheme.onBackground,
                    modifier = Modifier.size(48.dp))
                Text(
                    text = stringResource(R.string.songs),
                    color = MaterialTheme.colorScheme.onBackground,
                    fontWeight = FontWeight.Bold,
                    fontSize = MaterialTheme.typography.headlineLarge.fontSize
                )

                Spacer(modifier = Modifier.weight(1f))

                IconButton(onClick = { isSearchFieldOpen = !isSearchFieldOpen },
                    modifier = Modifier
                        .size(48.dp)) {
                    Icon(Icons.Rounded.Search, contentDescription = "Search all songs")
                }
            }

            HorizontalLineWithNavidromeCheck()

            AnimatedVisibility(
                visible = isSearchFieldOpen,
                enter = expandVertically(),
                exit = shrinkVertically()
            ) {
                Box(modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 12.dp)
                    .padding(bottom = 6.dp)
                ) {
                    val focusRequester = remember { FocusRequester() }
                    TextField(
                        value = searchFilter,
                        onValueChange = {
                            searchFilter = it
                            if (it.isBlank()){
                                coroutineScope.launch {
                                    viewModel.reloadData()
                                    isSearchFieldOpen = false
                                }
                            } },
                        label = { Text(stringResource(R.string.Action_Search)) },
                        singleLine = true,
                        keyboardOptions = KeyboardOptions(imeAction = ImeAction.Search),
                        keyboardActions = KeyboardActions(
                            onSearch = {
                                coroutineScope.launch {
                                    viewModel.search(searchFilter)
                                    isSearchFieldOpen = false
                                }
                            }
                        ),
                        modifier = Modifier
                            .fillMaxWidth()
                            .clip(RoundedCornerShape(12.dp))
                            .focusRequester(focusRequester))

                    LaunchedEffect(isSearchFieldOpen) {
                        if (isSearchFieldOpen)
                            focusRequester.requestFocus()
                        else
                            focusRequester.freeFocus()
                    }
                }
            }

            Column(modifier = Modifier.padding(horizontal = 12.dp)) {
                SongsHorizontalColumn(
                    songList = allSongsList,
                    onSongSelected = { songs, index ->
                        Log.d("SONGS_SCREEN", "=== SONG SELECTED ===")
                        Log.d("SONGS_SCREEN", "Song selected at index: $index")
                        Log.d("SONGS_SCREEN", "Songs list size: ${songs.size}")
                        Log.d("SONGS_SCREEN", "MediaController: $mediaController")
                        Log.d("SONGS_SCREEN", "MediaController connected: ${mediaController?.isConnected}")

                        if (songs.isNotEmpty() && index >= 0 && index < songs.size) {
                            val selectedSong = songs[index]
                            Log.d("SONGS_SCREEN", "Selected song: ${selectedSong.mediaMetadata.title}")
                            Log.d("SONGS_SCREEN", "Song MediaId: ${selectedSong.mediaId}")
                            Log.d("SONGS_SCREEN", "Song URI: ${selectedSong.localConfiguration?.uri}")
                        }

                        if (mediaController == null) {
                            Log.e("SONGS_SCREEN", "MediaController is null!")
                            return@SongsHorizontalColumn
                        }

                        if (mediaController.isConnected) {
                            Log.d("SONGS_SCREEN", "MediaController connected, starting playback")
                            SongHelper.play(songs, index, mediaController)
                        } else {
                            Log.w("SONGS_SCREEN", "MediaController not connected, retrying...")
                            // Retry after a short delay
                            kotlinx.coroutines.CoroutineScope(kotlinx.coroutines.Dispatchers.Main).launch {
                                var retryCount = 0
                                while (!mediaController.isConnected && retryCount < 5) {
                                    Log.d("SONGS_SCREEN", "Retry $retryCount: waiting for connection...")
                                    kotlinx.coroutines.delay(500)
                                    retryCount++
                                }

                                if (mediaController.isConnected) {
                                    Log.d("SONGS_SCREEN", "MediaController connected after $retryCount retries")
                                    SongHelper.play(songs, index, mediaController)
                                } else {
                                    Log.e("SONGS_SCREEN", "MediaController failed to connect after $retryCount retries")
                                }
                            }
                        }
                    },
                    isSearch = searchFilter.isNotBlank(),
                    viewModel = viewModel
                )
            }
        }
    }

    if(showAddSongToPlaylistDialog.value)
        AddSongToPlaylist(setShowDialog =  { showAddSongToPlaylistDialog.value = it } )
}