package com.craftworks.music.ui.viewmodels

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.media3.common.MediaItem
import com.craftworks.music.providers.getAlbums
import com.craftworks.music.providers.searchAlbum
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

class AlbumScreenViewModel : ViewModel(), ReloadableViewModel {
    private val _allAlbums = MutableStateFlow<List<MediaItem>>(emptyList())
    val allAlbums: StateFlow<List<MediaItem>> = _allAlbums.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private var hasLoadedData = false

    fun loadDataIfNeeded() {
        if (!hasLoadedData && !_isLoading.value) {
            reloadData()
        }
    }

    override fun reloadData() {
        if (_isLoading.value) return

        viewModelScope.launch {
            _isLoading.value = true
            try {
                Log.d("ALBUM_VM", "Loading albums with performance optimization...")

                // Load albums with better error handling and performance
                val albums = try {
                    getAlbums("alphabeticalByName", 20, 0, false) // Use cache when possible
                } catch (e: Exception) {
                    Log.e("ALBUM_VM", "Failed to load albums: ${e.message}", e)
                    emptyList()
                }

                // Sort with null safety
                _allAlbums.value = albums.sortedByDescending { album ->
                    album.mediaMetadata.extras?.getString("navidromeID")?.startsWith("Local_") ?: false
                }

                hasLoadedData = true
                Log.d("ALBUM_VM", "Loaded ${albums.size} albums")
            } catch (e: Exception) {
                Log.e("ALBUM_VM", "Error in reloadData: ${e.message}", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun getMoreAlbums(sort: String? = "alphabeticalByName" , size: Int){
        viewModelScope.launch {
            val albumOffset = _allAlbums.value.size
            val newAlbums = getAlbums(sort, size, albumOffset)
            _allAlbums.value += newAlbums
        }
    }

    suspend fun search(query: String){
        _allAlbums.value = searchAlbum(query)
    }
}