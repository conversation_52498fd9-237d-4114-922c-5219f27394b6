package com.craftworks.music.ui.viewmodels

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.media3.common.MediaItem
import com.craftworks.music.providers.getAlbums
import com.craftworks.music.providers.searchAlbum
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

class AlbumScreenViewModel : ViewModel(), ReloadableViewModel {
    private val _allAlbums = MutableStateFlow<List<MediaItem>>(emptyList())
    val allAlbums: StateFlow<List<MediaItem>> = _allAlbums.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private var hasLoadedData = false

    fun loadDataIfNeeded() {
        if (!hasLoadedData && !_isLoading.value) {
            reloadData()
        }
    }

    override fun reloadData() {
        if (_isLoading.value) return

        viewModelScope.launch {
            _isLoading.value = true
            try {
                coroutineScope {
                    val allAlbumsDeferred = async { getAlbums("alphabeticalByName", 20, 0, true) }

                    _allAlbums.value = allAlbumsDeferred.await().sortedByDescending {
                        it.mediaMetadata.extras?.getString("navidromeID")!!.startsWith("Local_")
                    }
                }
                hasLoadedData = true
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun getMoreAlbums(sort: String? = "alphabeticalByName" , size: Int){
        viewModelScope.launch {
            val albumOffset = _allAlbums.value.size
            val newAlbums = getAlbums(sort, size, albumOffset)
            _allAlbums.value += newAlbums
        }
    }

    suspend fun search(query: String){
        _allAlbums.value = searchAlbum(query)
    }
}