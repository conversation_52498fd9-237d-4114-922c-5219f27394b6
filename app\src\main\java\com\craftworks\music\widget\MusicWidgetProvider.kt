package com.craftworks.music.widget

import android.app.PendingIntent
import android.appwidget.AppWidgetManager
import android.appwidget.AppWidgetProvider
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.drawable.BitmapDrawable
import android.net.Uri
import android.util.Log
import android.util.LruCache
import android.widget.RemoteViews
import androidx.core.content.ContextCompat
import androidx.media3.common.MediaMetadata
import androidx.media3.common.Player
import com.craftworks.music.MainActivity
import com.craftworks.music.R
import com.craftworks.music.player.ChoraMediaLibraryService
import com.craftworks.music.player.SongHelper
import kotlinx.coroutines.*
import java.io.InputStream
import java.net.HttpURLConnection
import java.net.URL

class MusicWidgetProvider : AppWidgetProvider() {

    companion object {
        const val ACTION_PLAY_PAUSE = "com.craftworks.music.widget.PLAY_PAUSE"
        const val ACTION_NEXT = "com.craftworks.music.widget.NEXT"
        const val ACTION_PREVIOUS = "com.craftworks.music.widget.PREVIOUS"

        // Cache for album art bitmaps
        private val albumArtCache = LruCache<String, Bitmap>(20)
        private val widgetScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
        
        fun updateWidget(context: Context, isPlaying: Boolean, metadata: MediaMetadata?) {
            try {
                Log.d("WIDGET", "updateWidget called - isPlaying: $isPlaying, title: ${metadata?.title}")
                val appWidgetManager = AppWidgetManager.getInstance(context)
                val widgetComponent = ComponentName(context, MusicWidgetProvider::class.java)
                val widgetIds = appWidgetManager.getAppWidgetIds(widgetComponent)

                Log.d("WIDGET", "Found ${widgetIds.size} widget instances")
                if (widgetIds.isEmpty()) {
                    Log.w("WIDGET", "No widgets found. User needs to add widget to home screen manually.")
                    return
                }

                for (widgetId in widgetIds) {
                    Log.d("WIDGET", "Updating widget ID: $widgetId")
                    updateAppWidget(context, appWidgetManager, widgetId, isPlaying, metadata)
                }
                Log.d("WIDGET", "Widget updated successfully")
            } catch (e: Exception) {
                Log.e("WIDGET", "Error updating widget: ${e.message}", e)
            }
        }
        
        private fun updateAppWidget(
            context: Context,
            appWidgetManager: AppWidgetManager,
            appWidgetId: Int,
            isPlaying: Boolean,
            metadata: MediaMetadata?
        ) {
            Log.d("WIDGET", "=== UPDATE APP WIDGET ===")
            Log.d("WIDGET", "Widget ID: $appWidgetId, IsPlaying: $isPlaying")

            try {
                val views = RemoteViews(context.packageName, R.layout.music_widget)
                Log.d("WIDGET", "RemoteViews created successfully")

                // Update song info
                val title = metadata?.title?.toString() ?: "No song playing"
                val artist = metadata?.artist?.toString() ?: "Unknown Artist"

                Log.d("WIDGET", "Setting title: $title, artist: $artist")
                views.setTextViewText(R.id.widget_song_title, title)
                views.setTextViewText(R.id.widget_artist_name, artist)

                // Update play/pause button
                val playPauseIcon = if (isPlaying) R.drawable.ic_pause else R.drawable.ic_play_arrow
                Log.d("WIDGET", "Setting play/pause icon: $playPauseIcon")
                views.setImageViewResource(R.id.widget_play_pause, playPauseIcon)

                // Set up click intents
                Log.d("WIDGET", "Setting up click intents")
                setupClickIntents(context, views)

                // Update album art
                Log.d("WIDGET", "Updating album art")
                updateAlbumArt(context, views, metadata, appWidgetManager, appWidgetId)

                Log.d("WIDGET", "Calling appWidgetManager.updateAppWidget")
                appWidgetManager.updateAppWidget(appWidgetId, views)
                Log.d("WIDGET", "Widget $appWidgetId updated successfully")

            } catch (e: Exception) {
                Log.e("WIDGET", "Error in updateAppWidget: ${e.message}", e)
                throw e
            }
        }
        
        private fun setupClickIntents(context: Context, views: RemoteViews) {
            // Play/Pause button
            val playPauseIntent = Intent(context, MusicWidgetProvider::class.java).apply {
                action = ACTION_PLAY_PAUSE
            }
            val playPausePendingIntent = PendingIntent.getBroadcast(
                context, 0, playPauseIntent, 
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            views.setOnClickPendingIntent(R.id.widget_play_pause, playPausePendingIntent)
            
            // Previous button
            val previousIntent = Intent(context, MusicWidgetProvider::class.java).apply {
                action = ACTION_PREVIOUS
            }
            val previousPendingIntent = PendingIntent.getBroadcast(
                context, 1, previousIntent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            views.setOnClickPendingIntent(R.id.widget_previous, previousPendingIntent)
            
            // Next button
            val nextIntent = Intent(context, MusicWidgetProvider::class.java).apply {
                action = ACTION_NEXT
            }
            val nextPendingIntent = PendingIntent.getBroadcast(
                context, 2, nextIntent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            views.setOnClickPendingIntent(R.id.widget_next, nextPendingIntent)
            
            // Widget click to open app
            val openAppIntent = Intent(context, MainActivity::class.java)
            val openAppPendingIntent = PendingIntent.getActivity(
                context, 3, openAppIntent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            views.setOnClickPendingIntent(R.id.widget_album_art, openAppPendingIntent)
        }
        
        private fun updateAlbumArt(
            context: Context,
            views: RemoteViews,
            metadata: MediaMetadata?,
            appWidgetManager: AppWidgetManager,
            appWidgetId: Int
        ) {
            val artworkUri = metadata?.artworkUri
            if (artworkUri != null) {
                val uriString = artworkUri.toString()

                // Check cache first
                val cachedBitmap = albumArtCache.get(uriString)
                if (cachedBitmap != null) {
                    views.setImageViewBitmap(R.id.widget_album_art, cachedBitmap)
                    return
                }

                // Load artwork asynchronously
                widgetScope.launch {
                    try {
                        val bitmap = loadArtworkBitmap(context, artworkUri)

                        withContext(Dispatchers.Main) {
                            try {
                                if (bitmap != null) {
                                    // Cache the bitmap
                                    albumArtCache.put(uriString, bitmap)
                                    views.setImageViewBitmap(R.id.widget_album_art, bitmap)
                                } else {
                                    views.setImageViewResource(R.id.widget_album_art, R.drawable.ic_music_note)
                                }
                                appWidgetManager.updateAppWidget(appWidgetId, views)
                            } catch (e: Exception) {
                                Log.e("WIDGET", "Error updating album art UI: ${e.message}")
                            }
                        }
                    } catch (e: Exception) {
                        Log.e("WIDGET", "Error loading album art: ${e.message}")
                        withContext(Dispatchers.Main) {
                            try {
                                views.setImageViewResource(R.id.widget_album_art, R.drawable.ic_music_note)
                                appWidgetManager.updateAppWidget(appWidgetId, views)
                            } catch (ex: Exception) {
                                Log.e("WIDGET", "Error setting default album art: ${ex.message}")
                            }
                        }
                    }
                }
            } else {
                views.setImageViewResource(R.id.widget_album_art, R.drawable.ic_music_note)
            }
        }

        private suspend fun loadArtworkBitmap(context: Context, artworkUri: Uri): Bitmap? {
            return withContext(Dispatchers.IO) {
                try {
                    when (artworkUri.scheme) {
                        "content" -> {
                            // Handle content URIs (local files)
                            context.contentResolver.openInputStream(artworkUri)?.use { inputStream ->
                                BitmapFactory.decodeStream(inputStream)
                            }
                        }
                        "android.resource" -> {
                            // Handle resource URIs
                            context.contentResolver.openInputStream(artworkUri)?.use { inputStream ->
                                BitmapFactory.decodeStream(inputStream)
                            }
                        }
                        "http", "https" -> {
                            // Handle network URIs
                            val url = URL(artworkUri.toString())
                            val connection = url.openConnection() as HttpURLConnection
                            connection.connectTimeout = 5000
                            connection.readTimeout = 10000
                            connection.doInput = true
                            connection.connect()

                            if (connection.responseCode == HttpURLConnection.HTTP_OK) {
                                connection.inputStream.use { inputStream ->
                                    BitmapFactory.decodeStream(inputStream)
                                }
                            } else {
                                Log.w("WIDGET", "HTTP error ${connection.responseCode} loading artwork")
                                null
                            }
                        }
                        else -> {
                            Log.w("WIDGET", "Unsupported URI scheme: ${artworkUri.scheme}")
                            null
                        }
                    }
                } catch (e: Exception) {
                    Log.e("WIDGET", "Error loading artwork from $artworkUri: ${e.message}")
                    null
                }
            }
        }
    }

    override fun onUpdate(context: Context, appWidgetManager: AppWidgetManager, appWidgetIds: IntArray) {
        Log.d("WIDGET", "=== WIDGET UPDATE ===")
        Log.d("WIDGET", "onUpdate called for ${appWidgetIds.size} widgets")
        Log.d("WIDGET", "Widget IDs: ${appWidgetIds.contentToString()}")

        for (appWidgetId in appWidgetIds) {
            try {
                Log.d("WIDGET", "Updating widget ID: $appWidgetId")

                // Get current playing info from service
                val service = ChoraMediaLibraryService.getInstance()
                val player = service?.player
                val isPlaying = player?.isPlaying ?: false
                val metadata = player?.currentMediaItem?.mediaMetadata

                Log.d("WIDGET", "Service: $service, Player: $player, IsPlaying: $isPlaying")
                Log.d("WIDGET", "Current song: ${metadata?.title}")

                updateAppWidget(context, appWidgetManager, appWidgetId, isPlaying, metadata)
                Log.d("WIDGET", "Widget $appWidgetId updated successfully")
            } catch (e: Exception) {
                Log.e("WIDGET", "Error updating widget $appWidgetId: ${e.message}", e)
                // Fallback to default state
                updateAppWidget(context, appWidgetManager, appWidgetId, false, null)
            }
        }
    }

    override fun onEnabled(context: Context) {
        super.onEnabled(context)
        Log.d("WIDGET", "Widget enabled - first widget added")
        // Initialize widget with current playback state if available
        try {
            val mediaController = SongHelper.currentMediaController
            val isPlaying = mediaController?.isPlaying ?: false
            val metadata = mediaController?.currentMediaItem?.mediaMetadata
            updateWidget(context, isPlaying, metadata)
        } catch (e: Exception) {
            Log.e("WIDGET", "Error initializing widget on enable: ${e.message}", e)
        }
    }

    override fun onDisabled(context: Context) {
        super.onDisabled(context)
        Log.d("WIDGET", "Widget disabled - last widget removed")
        // Clear any cached resources
        albumArtCache.evictAll()
    }

    override fun onReceive(context: Context, intent: Intent) {
        super.onReceive(context, intent)
        
        when (intent.action) {
            ACTION_PLAY_PAUSE -> {
                try {
                    val controller = SongHelper.currentMediaController
                    val servicePlayer = ChoraMediaLibraryService.getInstance()?.player

                    when {
                        controller != null -> {
                            if (controller.isPlaying) {
                                controller.pause()
                            } else {
                                controller.play()
                            }
                            Log.d("WIDGET", "Play/Pause action executed via MediaController")
                        }
                        servicePlayer != null -> {
                            if (servicePlayer.isPlaying) {
                                servicePlayer.pause()
                            } else {
                                servicePlayer.play()
                            }
                            Log.d("WIDGET", "Play/Pause action executed via service player")
                        }
                        else -> {
                            Log.w("WIDGET", "No player available for play/pause")
                        }
                    }
                } catch (e: Exception) {
                    Log.e("WIDGET", "Error executing play/pause: ${e.message}", e)
                }
            }
            ACTION_NEXT -> {
                try {
                    val controller = SongHelper.currentMediaController
                    val servicePlayer = ChoraMediaLibraryService.getInstance()?.player

                    when {
                        controller != null -> {
                            controller.seekToNext()
                            Log.d("WIDGET", "Next action executed via MediaController")
                        }
                        servicePlayer != null -> {
                            servicePlayer.seekToNext()
                            Log.d("WIDGET", "Next action executed via service player")
                        }
                        else -> {
                            Log.w("WIDGET", "No player available for next")
                        }
                    }
                } catch (e: Exception) {
                    Log.e("WIDGET", "Error executing next: ${e.message}", e)
                }
            }
            ACTION_PREVIOUS -> {
                try {
                    val controller = SongHelper.currentMediaController
                    val servicePlayer = ChoraMediaLibraryService.getInstance()?.player

                    when {
                        controller != null -> {
                            controller.seekToPrevious()
                            Log.d("WIDGET", "Previous action executed via MediaController")
                        }
                        servicePlayer != null -> {
                            servicePlayer.seekToPrevious()
                            Log.d("WIDGET", "Previous action executed via service player")
                        }
                        else -> {
                            Log.w("WIDGET", "No player available for previous")
                        }
                    }
                } catch (e: Exception) {
                    Log.e("WIDGET", "Error executing previous: ${e.message}", e)
                }
            }
        }
    }
}
