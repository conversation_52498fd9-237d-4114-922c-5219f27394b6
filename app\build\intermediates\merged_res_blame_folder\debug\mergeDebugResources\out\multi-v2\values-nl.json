{"logs": [{"outputFile": "com.craftworks.music.app-mergeDebugResources-3:/values-nl/values-nl.xml", "map": [{"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\c9938815eb16e1bbe389a6512658438a\\transformed\\material3-release\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,177,294,415,533,633,731,846,998,1119,1261,1346,1445,1541,1644,1762,1883,1987,2118,2246,2382,2560,2691,2811,2932,3067,3164,3264,3384,3513,3613,3720,3823,3960,4100,4206,4310,4394,4494,4591,4702,4789,4876,4981,5061,5144,5243,5347,5442,5541,5629,5739,5840,5945,6065,6145,6246", "endColumns": "121,116,120,117,99,97,114,151,120,141,84,98,95,102,117,120,103,130,127,135,177,130,119,120,134,96,99,119,128,99,106,102,136,139,105,103,83,99,96,110,86,86,104,79,82,98,103,94,98,87,109,100,104,119,79,100,94", "endOffsets": "172,289,410,528,628,726,841,993,1114,1256,1341,1440,1536,1639,1757,1878,1982,2113,2241,2377,2555,2686,2806,2927,3062,3159,3259,3379,3508,3608,3715,3818,3955,4095,4201,4305,4389,4489,4586,4697,4784,4871,4976,5056,5139,5238,5342,5437,5536,5624,5734,5835,5940,6060,6140,6241,6336"}, "to": {"startLines": "74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6821,6943,7060,7181,7299,7399,7497,7612,7764,7885,8027,8112,8211,8307,8410,8528,8649,8753,8884,9012,9148,9326,9457,9577,9698,9833,9930,10030,10150,10279,10379,10486,10589,10726,10866,10972,11076,11160,11260,11357,11468,11555,11642,11747,11827,11910,12009,12113,12208,12307,12395,12505,12606,12711,12831,12911,13012", "endColumns": "121,116,120,117,99,97,114,151,120,141,84,98,95,102,117,120,103,130,127,135,177,130,119,120,134,96,99,119,128,99,106,102,136,139,105,103,83,99,96,110,86,86,104,79,82,98,103,94,98,87,109,100,104,119,79,100,94", "endOffsets": "6938,7055,7176,7294,7394,7492,7607,7759,7880,8022,8107,8206,8302,8405,8523,8644,8748,8879,9007,9143,9321,9452,9572,9693,9828,9925,10025,10145,10274,10374,10481,10584,10721,10861,10967,11071,11155,11255,11352,11463,11550,11637,11742,11822,11905,12004,12108,12203,12302,12390,12500,12601,12706,12826,12906,13007,13102"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\114a5d5301d7649ab21683cbabeecc89\\transformed\\media3-exoplayer-1.7.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,190,254,321,398,467,556,639", "endColumns": "70,63,63,66,76,68,88,82,71", "endOffsets": "121,185,249,316,393,462,551,634,706"}, "to": {"startLines": "61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "5777,5848,5912,5976,6043,6120,6189,6278,6361", "endColumns": "70,63,63,66,76,68,88,82,71", "endOffsets": "5843,5907,5971,6038,6115,6184,6273,6356,6428"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\493af54b1b0004569d1870cf1ba0af14\\transformed\\media3-session-1.7.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,137,234,310,417,515,605,700,800,897,974,1039,1139,1222,1319,1397,1488,1563,1656,1727,1797,1882,1968,2065", "endColumns": "81,96,75,106,97,89,94,99,96,76,64,99,82,96,77,90,74,92,70,69,84,85,96,98", "endOffsets": "132,229,305,412,510,600,695,795,892,969,1034,1134,1217,1314,1392,1483,1558,1651,1722,1792,1877,1963,2060,2159"}, "to": {"startLines": "41,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,132,133,134,135,136,137", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3992,4258,4355,4431,4538,4636,4726,4821,4921,5018,5095,5160,5260,5343,5440,5518,5609,5684,13197,13268,13338,13423,13509,13606", "endColumns": "81,96,75,106,97,89,94,99,96,76,64,99,82,96,77,90,74,92,70,69,84,85,96,98", "endOffsets": "4069,4350,4426,4533,4631,4721,4816,4916,5013,5090,5155,5255,5338,5435,5513,5604,5679,5772,13263,13333,13418,13504,13601,13700"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\c3709e4bfd5d4e5c1ed8a624d490cd12\\transformed\\material-release\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "89", "endOffsets": "140"}, "to": {"startLines": "131", "startColumns": "4", "startOffsets": "13107", "endColumns": "89", "endOffsets": "13192"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\4f725fd807947c49350c5a696b32f0e9\\transformed\\preference-1.2.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,267,348,494,663,743", "endColumns": "71,89,80,145,168,79,76", "endOffsets": "172,262,343,489,658,738,815"}, "to": {"startLines": "39,70,174,175,185,191,192", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3823,6433,17906,17987,18862,19477,19557", "endColumns": "71,89,80,145,168,79,76", "endOffsets": "3890,6518,17982,18128,19026,19552,19629"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\b12f5ec7860c79fe319ea01d980e4aac\\transformed\\ui-release\\res\\values-nl\\values-nl.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "193,285,368,465,564,649,725,821,908,997,1078,1161,1238,1324,1399,1471,1542,1626,1696", "endColumns": "91,82,96,98,84,75,95,86,88,80,82,76,85,74,71,70,83,69,119", "endOffsets": "280,363,460,559,644,720,816,903,992,1073,1156,1233,1319,1394,1466,1537,1621,1691,1811"}, "to": {"startLines": "37,38,40,42,43,72,73,172,173,176,177,179,180,181,182,183,186,187,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3648,3740,3895,4074,4173,6649,6725,17730,17817,18133,18214,18380,18457,18543,18618,18690,19031,19115,19185", "endColumns": "91,82,96,98,84,75,95,86,88,80,82,76,85,74,71,70,83,69,119", "endOffsets": "3735,3818,3987,4168,4253,6720,6816,17812,17901,18209,18292,18452,18538,18613,18685,18756,19110,19180,19300"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\886fef481c8ce4a2616ccf22a8cd1328\\transformed\\foundation-release\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,142,228", "endColumns": "86,85,85", "endOffsets": "137,223,309"}, "to": {"startLines": "29,189,190", "startColumns": "4,4,4", "startOffsets": "2827,19305,19391", "endColumns": "86,85,85", "endOffsets": "2909,19386,19472"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\14cf378553861cbecadb791c6e2341a9\\transformed\\core-1.16.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,359,459,566,670,789", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "152,254,354,454,561,665,784,885"}, "to": {"startLines": "30,31,32,33,34,35,36,184", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2914,3016,3118,3218,3318,3425,3529,18761", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "3011,3113,3213,3313,3420,3524,3643,18857"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\64a01e76fd68fe16459fb0e17d50396b\\transformed\\appcompat-1.6.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,328,435,520,624,744,822,898,990,1084,1179,1273,1373,1467,1563,1658,1750,1842,1924,2035,2138,2237,2352,2466,2569,2724,2827", "endColumns": "117,104,106,84,103,119,77,75,91,93,94,93,99,93,95,94,91,91,81,110,102,98,114,113,102,154,102,82", "endOffsets": "218,323,430,515,619,739,817,893,985,1079,1174,1268,1368,1462,1558,1653,1745,1837,1919,2030,2133,2232,2347,2461,2564,2719,2822,2905"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,178", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,328,435,520,624,744,822,898,990,1084,1179,1273,1373,1467,1563,1658,1750,1842,1924,2035,2138,2237,2352,2466,2569,2724,18297", "endColumns": "117,104,106,84,103,119,77,75,91,93,94,93,99,93,95,94,91,91,81,110,102,98,114,113,102,154,102,82", "endOffsets": "218,323,430,515,619,739,817,893,985,1079,1174,1268,1368,1462,1558,1653,1745,1837,1919,2030,2133,2232,2347,2461,2564,2719,2822,18375"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\2fbbcb27eaccaf382f754b7d51fec492\\transformed\\mediarouter-1.7.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,181,276,378,488,604,728,837,933,1020,1200,1381,1568,1754,1932,2120,2309,2429,2521,2622,2720,2819,2923,3018,3135,3249,3335,3421,3506,3605,3712,3805,3913,4023,4110", "endColumns": "125,94,101,109,115,123,108,95,86,179,180,186,185,177,187,188,119,91,100,97,98,103,94,116,113,85,85,84,98,106,92,107,109,86,95", "endOffsets": "176,271,373,483,599,723,832,928,1015,1195,1376,1563,1749,1927,2115,2304,2424,2516,2617,2715,2814,2918,3013,3130,3244,3330,3416,3501,3600,3707,3800,3908,4018,4105,4201"}, "to": {"startLines": "71,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6523,13705,13800,13902,14012,14128,14252,14361,14457,14544,14724,14905,15092,15278,15456,15644,15833,15953,16045,16146,16244,16343,16447,16542,16659,16773,16859,16945,17030,17129,17236,17329,17437,17547,17634", "endColumns": "125,94,101,109,115,123,108,95,86,179,180,186,185,177,187,188,119,91,100,97,98,103,94,116,113,85,85,84,98,106,92,107,109,86,95", "endOffsets": "6644,13795,13897,14007,14123,14247,14356,14452,14539,14719,14900,15087,15273,15451,15639,15828,15948,16040,16141,16239,16338,16442,16537,16654,16768,16854,16940,17025,17124,17231,17324,17432,17542,17629,17725"}}]}]}