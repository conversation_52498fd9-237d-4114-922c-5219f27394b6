![Logo](https://github.com/CraftWorksMC/Chora/blob/master/Github/Images/ChoraBannerTransparent.png?raw=true)

A simple and light-weight app that streams music from a Subsonic or Navidrome server, or from the phone's storage.

*Please do not use as a learning resource. This was my first Kotlin project, and the code is not well-organized at all.*

<a href='https://play.google.com/store/apps/details?id=com.craftworks.music&pcampaignid=pcampaignidMKT-Other-global-all-co-prtnr-py-PartBadge-Mar2515-1'><img width=256px alt='Get it on Google Play' src='https://play.google.com/intl/en_us/badges/static/images/badges/en_badge_web_generic.png'/></a>

## Features

- Subsonic/Navidrome support.
- Transcoding.
- Material 3 UI.
- Offline Mode [Download songs from server].
- Internet Radio. [Metadata IceCast only]
- Synced And Unsynced Lyrics. [From lrclib.net]
- Navidrome and Local playlists.
- Android Auto.

## W.I.P

- Plain lyrics auto-scrolling.

## Known Issues

- After changing some settings, all the data is cleared from screens and need to be manually refreshed.

## Roadmap

- Jellyfin (Music) Support.

## Screenshots
<p align="center">
    <img src="https://github.com/CraftWorksMC/Chora/blob/master/Github/Images/Now-Playing-Screen.png?raw=true" width=200>
    <img src="https://github.com/CraftWorksMC/Chora/blob/master/Github/Images/Now-Playing-SyncedLyrics.png?raw=true" width=200>
    <img src="https://github.com/CraftWorksMC/Chora/blob/master/Github/Images/Now-Playing-PlainLyrics.png?raw=true" width=200>
    <img src="https://github.com/CraftWorksMC/Chora/blob/master/Github/Images/HomeScreen.png?raw=true" width=200>
    <img src="https://github.com/CraftWorksMC/Chora/blob/master/Github/Images/PlaylistScreen.png?raw=true" width=200>
    <img src="https://github.com/CraftWorksMC/Chora/blob/master/Github/Images/AlbumScreen.png?raw=true" width=200>
    <img src="https://github.com/CraftWorksMC/Chora/blob/master/Github/Images/RadioScreen.png?raw=true" width=200>
    <img src="https://github.com/CraftWorksMC/Chora/blob/master/Github/Images/SettingScreen.png?raw=true" width=200>
</p>


## Support the project

To help keep this project free and open source to everyone, consider donating. Thank you!  
<a href="https://www.paypal.com/donate/?hosted_button_id=REWCVJBKECU34">
  <img width=256px src="https://raw.githubusercontent.com/stefan-niedermann/paypal-donate-button/master/paypal-donate-button.png" alt="Donate with PayPal" />
</a>

Made with :heart: in italy

> Lyrics icon provided by [Remix Icon](https://remixicon.com/ "Remix Icon")
> Other icons are provided by [Google Icons](https://fonts.google.com/icons "Google Icons")