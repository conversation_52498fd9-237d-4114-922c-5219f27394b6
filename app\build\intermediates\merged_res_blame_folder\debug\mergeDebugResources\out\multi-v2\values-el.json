{"logs": [{"outputFile": "com.craftworks.music.app-mergeDebugResources-3:/values-el/values-el.xml", "map": [{"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\c3709e4bfd5d4e5c1ed8a624d490cd12\\transformed\\material-release\\res\\values-el\\values-el.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "92", "endOffsets": "143"}, "to": {"startLines": "131", "startColumns": "4", "startOffsets": "13566", "endColumns": "92", "endOffsets": "13654"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\493af54b1b0004569d1870cf1ba0af14\\transformed\\media3-session-1.7.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,249,329,435,548,639,729,831,934,1047,1114,1231,1351,1460,1547,1642,1718,1831,1899,1972,2061,2155,2256", "endColumns": "76,116,79,105,112,90,89,101,102,112,66,116,119,108,86,94,75,112,67,72,88,93,100,108", "endOffsets": "127,244,324,430,543,634,724,826,929,1042,1109,1226,1346,1455,1542,1637,1713,1826,1894,1967,2056,2150,2251,2360"}, "to": {"startLines": "41,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,132,133,134,135,136,137", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4066,4340,4457,4537,4643,4756,4847,4937,5039,5142,5255,5322,5439,5559,5668,5755,5850,5926,13659,13727,13800,13889,13983,14084", "endColumns": "76,116,79,105,112,90,89,101,102,112,66,116,119,108,86,94,75,112,67,72,88,93,100,108", "endOffsets": "4138,4452,4532,4638,4751,4842,4932,5034,5137,5250,5317,5434,5554,5663,5750,5845,5921,6034,13722,13795,13884,13978,14079,14188"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\c9938815eb16e1bbe389a6512658438a\\transformed\\material3-release\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,179,303,417,539,643,746,866,1017,1145,1303,1393,1493,1592,1697,1815,1941,2046,2188,2324,2468,2648,2786,2906,3033,3157,3257,3356,3492,3629,3735,3841,3951,4095,4248,4362,4468,4555,4653,4750,4863,4953,5042,5145,5225,5308,5407,5509,5606,5704,5791,5897,5996,6098,6219,6299,6415", "endColumns": "123,123,113,121,103,102,119,150,127,157,89,99,98,104,117,125,104,141,135,143,179,137,119,126,123,99,98,135,136,105,105,109,143,152,113,105,86,97,96,112,89,88,102,79,82,98,101,96,97,86,105,98,101,120,79,115,106", "endOffsets": "174,298,412,534,638,741,861,1012,1140,1298,1388,1488,1587,1692,1810,1936,2041,2183,2319,2463,2643,2781,2901,3028,3152,3252,3351,3487,3624,3730,3836,3946,4090,4243,4357,4463,4550,4648,4745,4858,4948,5037,5140,5220,5303,5402,5504,5601,5699,5786,5892,5991,6093,6214,6294,6410,6517"}, "to": {"startLines": "74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7099,7223,7347,7461,7583,7687,7790,7910,8061,8189,8347,8437,8537,8636,8741,8859,8985,9090,9232,9368,9512,9692,9830,9950,10077,10201,10301,10400,10536,10673,10779,10885,10995,11139,11292,11406,11512,11599,11697,11794,11907,11997,12086,12189,12269,12352,12451,12553,12650,12748,12835,12941,13040,13142,13263,13343,13459", "endColumns": "123,123,113,121,103,102,119,150,127,157,89,99,98,104,117,125,104,141,135,143,179,137,119,126,123,99,98,135,136,105,105,109,143,152,113,105,86,97,96,112,89,88,102,79,82,98,101,96,97,86,105,98,101,120,79,115,106", "endOffsets": "7218,7342,7456,7578,7682,7785,7905,8056,8184,8342,8432,8532,8631,8736,8854,8980,9085,9227,9363,9507,9687,9825,9945,10072,10196,10296,10395,10531,10668,10774,10880,10990,11134,11287,11401,11507,11594,11692,11789,11902,11992,12081,12184,12264,12347,12446,12548,12645,12743,12830,12936,13035,13137,13258,13338,13454,13561"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\b12f5ec7860c79fe319ea01d980e4aac\\transformed\\ui-release\\res\\values-el\\values-el.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "192,291,377,478,583,675,756,850,939,1029,1110,1192,1267,1356,1431,1509,1584,1663,1733", "endColumns": "98,85,100,104,91,80,93,88,89,80,81,74,88,74,77,74,78,69,122", "endOffsets": "286,372,473,578,670,751,845,934,1024,1105,1187,1262,1351,1426,1504,1579,1658,1728,1851"}, "to": {"startLines": "37,38,40,42,43,72,73,172,173,176,177,179,180,181,182,183,186,187,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3707,3806,3965,4143,4248,6924,7005,18263,18352,18667,18748,18916,18991,19080,19155,19233,19578,19657,19727", "endColumns": "98,85,100,104,91,80,93,88,89,80,81,74,88,74,77,74,78,69,122", "endOffsets": "3801,3887,4061,4243,4335,7000,7094,18347,18437,18743,18825,18986,19075,19150,19228,19303,19652,19722,19845"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\114a5d5301d7649ab21683cbabeecc89\\transformed\\media3-exoplayer-1.7.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,184,242,305,379,455,554,649", "endColumns": "70,57,57,62,73,75,98,94,66", "endOffsets": "121,179,237,300,374,450,549,644,711"}, "to": {"startLines": "61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6039,6110,6168,6226,6289,6363,6439,6538,6633", "endColumns": "70,57,57,62,73,75,98,94,66", "endOffsets": "6105,6163,6221,6284,6358,6434,6533,6628,6695"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\4f725fd807947c49350c5a696b32f0e9\\transformed\\preference-1.2.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,178,265,348,490,659,744", "endColumns": "72,86,82,141,168,84,79", "endOffsets": "173,260,343,485,654,739,819"}, "to": {"startLines": "39,70,174,175,185,191,192", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3892,6700,18442,18525,19409,20050,20135", "endColumns": "72,86,82,141,168,84,79", "endOffsets": "3960,6782,18520,18662,19573,20130,20210"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\2fbbcb27eaccaf382f754b7d51fec492\\transformed\\mediarouter-1.7.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,192,289,392,494,603,735,844,939,1026,1209,1400,1591,1781,1971,2164,2352,2478,2577,2680,2779,2875,2969,3063,3188,3299,3382,3471,3556,3658,3768,3861,3964,4080,4167", "endColumns": "136,96,102,101,108,131,108,94,86,182,190,190,189,189,192,187,125,98,102,98,95,93,93,124,110,82,88,84,101,109,92,102,115,86,94", "endOffsets": "187,284,387,489,598,730,839,934,1021,1204,1395,1586,1776,1966,2159,2347,2473,2572,2675,2774,2870,2964,3058,3183,3294,3377,3466,3551,3653,3763,3856,3959,4075,4162,4257"}, "to": {"startLines": "71,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6787,14193,14290,14393,14495,14604,14736,14845,14940,15027,15210,15401,15592,15782,15972,16165,16353,16479,16578,16681,16780,16876,16970,17064,17189,17300,17383,17472,17557,17659,17769,17862,17965,18081,18168", "endColumns": "136,96,102,101,108,131,108,94,86,182,190,190,189,189,192,187,125,98,102,98,95,93,93,124,110,82,88,84,101,109,92,102,115,86,94", "endOffsets": "6919,14285,14388,14490,14599,14731,14840,14935,15022,15205,15396,15587,15777,15967,16160,16348,16474,16573,16676,16775,16871,16965,17059,17184,17295,17378,17467,17552,17654,17764,17857,17960,18076,18163,18258"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\64a01e76fd68fe16459fb0e17d50396b\\transformed\\appcompat-1.6.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,334,451,536,642,765,854,939,1030,1123,1218,1312,1412,1505,1600,1697,1788,1879,1964,2075,2184,2286,2397,2507,2615,2786,2886", "endColumns": "117,110,116,84,105,122,88,84,90,92,94,93,99,92,94,96,90,90,84,110,108,101,110,109,107,170,99,85", "endOffsets": "218,329,446,531,637,760,849,934,1025,1118,1213,1307,1407,1500,1595,1692,1783,1874,1959,2070,2179,2281,2392,2502,2610,2781,2881,2967"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,178", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,334,451,536,642,765,854,939,1030,1123,1218,1312,1412,1505,1600,1697,1788,1879,1964,2075,2184,2286,2397,2507,2615,2786,18830", "endColumns": "117,110,116,84,105,122,88,84,90,92,94,93,99,92,94,96,90,90,84,110,108,101,110,109,107,170,99,85", "endOffsets": "218,329,446,531,637,760,849,934,1025,1118,1213,1307,1407,1500,1595,1692,1783,1874,1959,2070,2179,2281,2392,2502,2610,2781,2881,18911"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\14cf378553861cbecadb791c6e2341a9\\transformed\\core-1.16.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,567,673,790", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "148,251,351,454,562,668,785,886"}, "to": {"startLines": "30,31,32,33,34,35,36,184", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2972,3070,3173,3273,3376,3484,3590,19308", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "3065,3168,3268,3371,3479,3585,3702,19404"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\886fef481c8ce4a2616ccf22a8cd1328\\transformed\\foundation-release\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,141,239", "endColumns": "85,97,101", "endOffsets": "136,234,336"}, "to": {"startLines": "29,189,190", "startColumns": "4,4,4", "startOffsets": "2886,19850,19948", "endColumns": "85,97,101", "endOffsets": "2967,19943,20045"}}]}]}