{"logs": [{"outputFile": "com.craftworks.music.app-mergeDebugResources-3:/values-hu/values-hu.xml", "map": [{"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\4f725fd807947c49350c5a696b32f0e9\\transformed\\preference-1.2.1\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,264,346,483,652,731", "endColumns": "70,87,81,136,168,78,75", "endOffsets": "171,259,341,478,647,726,802"}, "to": {"startLines": "39,70,174,175,185,191,192", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3851,6516,18142,18224,19100,19721,19800", "endColumns": "70,87,81,136,168,78,75", "endOffsets": "3917,6599,18219,18356,19264,19795,19871"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\493af54b1b0004569d1870cf1ba0af14\\transformed\\media3-session-1.7.1\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,140,249,325,423,531,618,701,792,888,971,1039,1137,1225,1320,1402,1498,1575,1667,1743,1814,1893,1974,2068", "endColumns": "84,108,75,97,107,86,82,90,95,82,67,97,87,94,81,95,76,91,75,70,78,80,93,94", "endOffsets": "135,244,320,418,526,613,696,787,883,966,1034,1132,1220,1315,1397,1493,1570,1662,1738,1809,1888,1969,2063,2158"}, "to": {"startLines": "41,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,132,133,134,135,136,137", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4019,4290,4399,4475,4573,4681,4768,4851,4942,5038,5121,5189,5287,5375,5470,5552,5648,5725,13339,13415,13486,13565,13646,13740", "endColumns": "84,108,75,97,107,86,82,90,95,82,67,97,87,94,81,95,76,91,75,70,78,80,93,94", "endOffsets": "4099,4394,4470,4568,4676,4763,4846,4937,5033,5116,5184,5282,5370,5465,5547,5643,5720,5812,13410,13481,13560,13641,13735,13830"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\b12f5ec7860c79fe319ea01d980e4aac\\transformed\\ui-release\\res\\values-hu\\values-hu.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "193,288,376,473,572,659,741,837,926,1013,1096,1184,1258,1351,1426,1497,1567,1646,1712", "endColumns": "94,87,96,98,86,81,95,88,86,82,87,73,92,74,70,69,78,65,120", "endOffsets": "283,371,468,567,654,736,832,921,1008,1091,1179,1253,1346,1421,1492,1562,1641,1707,1828"}, "to": {"startLines": "37,38,40,42,43,72,73,172,173,176,177,179,180,181,182,183,186,187,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3668,3763,3922,4104,4203,6734,6816,17966,18055,18361,18444,18616,18690,18783,18858,18929,19269,19348,19414", "endColumns": "94,87,96,98,86,81,95,88,86,82,87,73,92,74,70,69,78,65,120", "endOffsets": "3758,3846,4014,4198,4285,6811,6907,18050,18137,18439,18527,18685,18778,18853,18924,18994,19343,19409,19530"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\114a5d5301d7649ab21683cbabeecc89\\transformed\\media3-exoplayer-1.7.1\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,192,266,338,416,489,583,673", "endColumns": "74,61,73,71,77,72,93,89,80", "endOffsets": "125,187,261,333,411,484,578,668,749"}, "to": {"startLines": "61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "5817,5892,5954,6028,6100,6178,6251,6345,6435", "endColumns": "74,61,73,71,77,72,93,89,80", "endOffsets": "5887,5949,6023,6095,6173,6246,6340,6430,6511"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\c3709e4bfd5d4e5c1ed8a624d490cd12\\transformed\\material-release\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "96", "endOffsets": "147"}, "to": {"startLines": "131", "startColumns": "4", "startOffsets": "13242", "endColumns": "96", "endOffsets": "13334"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\c9938815eb16e1bbe389a6512658438a\\transformed\\material3-release\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,176,292,400,516,611,708,822,962,1085,1232,1317,1417,1515,1617,1739,1876,1981,2121,2259,2385,2581,2704,2826,2948,3074,3173,3268,3387,3524,3626,3737,3841,3986,4133,4240,4347,4431,4529,4623,4731,4819,4906,5007,5088,5171,5270,5376,5471,5574,5660,5769,5867,5973,6094,6175,6287", "endColumns": "120,115,107,115,94,96,113,139,122,146,84,99,97,101,121,136,104,139,137,125,195,122,121,121,125,98,94,118,136,101,110,103,144,146,106,106,83,97,93,107,87,86,100,80,82,98,105,94,102,85,108,97,105,120,80,111,97", "endOffsets": "171,287,395,511,606,703,817,957,1080,1227,1312,1412,1510,1612,1734,1871,1976,2116,2254,2380,2576,2699,2821,2943,3069,3168,3263,3382,3519,3621,3732,3836,3981,4128,4235,4342,4426,4524,4618,4726,4814,4901,5002,5083,5166,5265,5371,5466,5569,5655,5764,5862,5968,6089,6170,6282,6380"}, "to": {"startLines": "74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6912,7033,7149,7257,7373,7468,7565,7679,7819,7942,8089,8174,8274,8372,8474,8596,8733,8838,8978,9116,9242,9438,9561,9683,9805,9931,10030,10125,10244,10381,10483,10594,10698,10843,10990,11097,11204,11288,11386,11480,11588,11676,11763,11864,11945,12028,12127,12233,12328,12431,12517,12626,12724,12830,12951,13032,13144", "endColumns": "120,115,107,115,94,96,113,139,122,146,84,99,97,101,121,136,104,139,137,125,195,122,121,121,125,98,94,118,136,101,110,103,144,146,106,106,83,97,93,107,87,86,100,80,82,98,105,94,102,85,108,97,105,120,80,111,97", "endOffsets": "7028,7144,7252,7368,7463,7560,7674,7814,7937,8084,8169,8269,8367,8469,8591,8728,8833,8973,9111,9237,9433,9556,9678,9800,9926,10025,10120,10239,10376,10478,10589,10693,10838,10985,11092,11199,11283,11381,11475,11583,11671,11758,11859,11940,12023,12122,12228,12323,12426,12512,12621,12719,12825,12946,13027,13139,13237"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\2fbbcb27eaccaf382f754b7d51fec492\\transformed\\mediarouter-1.7.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,185,282,391,498,607,720,828,925,1014,1197,1396,1599,1804,2001,2206,2406,2531,2625,2740,2838,2937,3032,3126,3232,3355,3439,3526,3613,3717,3820,3915,4018,4133,4221", "endColumns": "129,96,108,106,108,112,107,96,88,182,198,202,204,196,204,199,124,93,114,97,98,94,93,105,122,83,86,86,103,102,94,102,114,87,94", "endOffsets": "180,277,386,493,602,715,823,920,1009,1192,1391,1594,1799,1996,2201,2401,2526,2620,2735,2833,2932,3027,3121,3227,3350,3434,3521,3608,3712,3815,3910,4013,4128,4216,4311"}, "to": {"startLines": "71,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6604,13835,13932,14041,14148,14257,14370,14478,14575,14664,14847,15046,15249,15454,15651,15856,16056,16181,16275,16390,16488,16587,16682,16776,16882,17005,17089,17176,17263,17367,17470,17565,17668,17783,17871", "endColumns": "129,96,108,106,108,112,107,96,88,182,198,202,204,196,204,199,124,93,114,97,98,94,93,105,122,83,86,86,103,102,94,102,114,87,94", "endOffsets": "6729,13927,14036,14143,14252,14365,14473,14570,14659,14842,15041,15244,15449,15646,15851,16051,16176,16270,16385,16483,16582,16677,16771,16877,17000,17084,17171,17258,17362,17465,17560,17663,17778,17866,17961"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\14cf378553861cbecadb791c6e2341a9\\transformed\\core-1.16.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,356,457,560,667,777", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "147,249,351,452,555,662,772,873"}, "to": {"startLines": "30,31,32,33,34,35,36,184", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2946,3043,3145,3247,3348,3451,3558,18999", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "3038,3140,3242,3343,3446,3553,3663,19095"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\64a01e76fd68fe16459fb0e17d50396b\\transformed\\appcompat-1.6.1\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,305,420,504,619,742,819,894,985,1078,1173,1267,1367,1460,1555,1650,1741,1832,1915,2025,2135,2235,2346,2455,2574,2756,2859", "endColumns": "107,91,114,83,114,122,76,74,90,92,94,93,99,92,94,94,90,90,82,109,109,99,110,108,118,181,102,83", "endOffsets": "208,300,415,499,614,737,814,889,980,1073,1168,1262,1362,1455,1550,1645,1736,1827,1910,2020,2130,2230,2341,2450,2569,2751,2854,2938"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,178", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,305,420,504,619,742,819,894,985,1078,1173,1267,1367,1460,1555,1650,1741,1832,1915,2025,2135,2235,2346,2455,2574,2756,18532", "endColumns": "107,91,114,83,114,122,76,74,90,92,94,93,99,92,94,94,90,90,82,109,109,99,110,108,118,181,102,83", "endOffsets": "208,300,415,499,614,737,814,889,980,1073,1168,1262,1362,1455,1550,1645,1736,1827,1910,2020,2130,2230,2341,2450,2569,2751,2854,18611"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\886fef481c8ce4a2616ccf22a8cd1328\\transformed\\foundation-release\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,142,231", "endColumns": "86,88,96", "endOffsets": "137,226,323"}, "to": {"startLines": "29,189,190", "startColumns": "4,4,4", "startOffsets": "2859,19535,19624", "endColumns": "86,88,96", "endOffsets": "2941,19619,19716"}}]}]}