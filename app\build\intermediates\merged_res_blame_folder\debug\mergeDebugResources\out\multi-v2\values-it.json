{"logs": [{"outputFile": "com.craftworks.music.app-mergeDebugResources-3:/values-it/values-it.xml", "map": [{"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\493af54b1b0004569d1870cf1ba0af14\\transformed\\media3-session-1.7.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,137,230,309,412,508,598,677,774,864,970,1038,1151,1251,1340,1415,1514,1601,1695,1772,1843,1922,2002,2100", "endColumns": "81,92,78,102,95,89,78,96,89,105,67,112,99,88,74,98,86,93,76,70,78,79,97,101", "endOffsets": "132,225,304,407,503,593,672,769,859,965,1033,1146,1246,1335,1410,1509,1596,1690,1767,1838,1917,1997,2095,2197"}, "to": {"startLines": "112,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,203,204,205,206,207,208", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8617,8886,8979,9058,9161,9257,9347,9426,9523,9613,9719,9787,9900,10000,10089,10164,10263,10350,18021,18098,18169,18248,18328,18426", "endColumns": "81,92,78,102,95,89,78,96,89,105,67,112,99,88,74,98,86,93,76,70,78,79,97,101", "endOffsets": "8694,8974,9053,9156,9252,9342,9421,9518,9608,9714,9782,9895,9995,10084,10159,10258,10345,10439,18093,18164,18243,18323,18421,18523"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\114a5d5301d7649ab21683cbabeecc89\\transformed\\media3-exoplayer-1.7.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,187,259,329,405,471,558,643", "endColumns": "70,60,71,69,75,65,86,84,73", "endOffsets": "121,182,254,324,400,466,553,638,712"}, "to": {"startLines": "132,133,134,135,136,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "10444,10515,10576,10648,10718,10794,10860,10947,11032", "endColumns": "70,60,71,69,75,65,86,84,73", "endOffsets": "10510,10571,10643,10713,10789,10855,10942,11027,11101"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\c9938815eb16e1bbe389a6512658438a\\transformed\\material3-release\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,182,306,428,552,657,753,866,1009,1128,1286,1370,1482,1576,1676,1795,1917,2034,2176,2316,2459,2635,2770,2890,3013,3143,3238,3335,3462,3600,3700,3810,3916,4059,4207,4317,4418,4507,4603,4696,4811,4897,4983,5086,5166,5249,5348,5454,5554,5655,5743,5853,5953,6058,6176,6256,6370", "endColumns": "126,123,121,123,104,95,112,142,118,157,83,111,93,99,118,121,116,141,139,142,175,134,119,122,129,94,96,126,137,99,109,105,142,147,109,100,88,95,92,114,85,85,102,79,82,98,105,99,100,87,109,99,104,117,79,113,106", "endOffsets": "177,301,423,547,652,748,861,1004,1123,1281,1365,1477,1571,1671,1790,1912,2029,2171,2311,2454,2630,2765,2885,3008,3138,3233,3330,3457,3595,3695,3805,3911,4054,4202,4312,4413,4502,4598,4691,4806,4892,4978,5081,5161,5244,5343,5449,5549,5650,5738,5848,5948,6053,6171,6251,6365,6472"}, "to": {"startLines": "145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "11510,11637,11761,11883,12007,12112,12208,12321,12464,12583,12741,12825,12937,13031,13131,13250,13372,13489,13631,13771,13914,14090,14225,14345,14468,14598,14693,14790,14917,15055,15155,15265,15371,15514,15662,15772,15873,15962,16058,16151,16266,16352,16438,16541,16621,16704,16803,16909,17009,17110,17198,17308,17408,17513,17631,17711,17825", "endColumns": "126,123,121,123,104,95,112,142,118,157,83,111,93,99,118,121,116,141,139,142,175,134,119,122,129,94,96,126,137,99,109,105,142,147,109,100,88,95,92,114,85,85,102,79,82,98,105,99,100,87,109,99,104,117,79,113,106", "endOffsets": "11632,11756,11878,12002,12107,12203,12316,12459,12578,12736,12820,12932,13026,13126,13245,13367,13484,13626,13766,13909,14085,14220,14340,14463,14593,14688,14785,14912,15050,15150,15260,15366,15509,15657,15767,15868,15957,16053,16146,16261,16347,16433,16536,16616,16699,16798,16904,17004,17105,17193,17303,17403,17508,17626,17706,17820,17927"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\c3709e4bfd5d4e5c1ed8a624d490cd12\\transformed\\material-release\\res\\values-it\\values-it.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "88", "endOffsets": "139"}, "to": {"startLines": "202", "startColumns": "4", "startOffsets": "17932", "endColumns": "88", "endOffsets": "18016"}}, {"source": "E:\\NAVIchora\\Chora\\app\\src\\main\\res\\values-it\\strings.xml", "from": {"startLines": "27,31,24,79,30,60,29,23,25,21,22,26,28,66,67,61,45,44,11,19,62,18,3,12,17,76,10,9,46,5,47,8,6,7,48,20,16,15,13,14,55,56,58,59,65,73,64,71,72,63,49,50,40,81,42,41,57,74,80,54,53,51,52,75,38,39,43,4,77,78,2,35,70,69,37,33,36,68,34,32", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1598,1795,1448,5037,1749,3835,1700,1397,1495,1296,1348,1546,1647,4249,4291,3883,2666,2611,630,1155,3936,1085,107,700,1021,4896,568,476,2719,226,2828,413,286,350,2894,1232,942,875,761,819,3402,3456,3615,3700,4158,4640,4080,4486,4557,4007,2976,3036,2335,5169,2480,2396,3542,4745,5090,3328,3248,3099,3164,4819,2208,2272,2544,168,4943,4989,57,2019,4438,4387,2142,1912,2074,4336,1978,1860", "endColumns": "47,63,45,51,44,46,47,49,49,50,47,50,51,40,43,51,51,53,68,75,69,68,59,59,62,45,60,90,107,58,64,61,62,61,80,62,77,65,56,54,52,84,83,133,89,103,76,69,81,71,58,61,59,82,62,82,71,72,77,72,78,63,82,75,62,61,65,56,44,46,48,53,46,49,64,64,66,49,39,50", "endOffsets": "1641,1854,1489,5084,1789,3877,1743,1442,1540,1342,1391,1592,1694,4285,4330,3930,2713,2660,694,1226,4001,1149,162,755,1079,4937,624,562,2822,280,2888,470,344,407,2970,1290,1015,936,813,869,3450,3536,3694,3829,4243,4739,4152,4551,4634,4074,3030,3093,2390,5247,2538,2474,3609,4813,5163,3396,3322,3158,3242,4890,2266,2329,2605,220,4983,5031,101,2068,4480,4432,2202,1972,2136,4381,2013,1906"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,209,247,249,250,253,254,257,259,272", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,153,217,263,315,360,407,455,505,555,606,654,705,757,798,842,894,946,1000,1069,1145,1215,1284,1344,1404,1467,1513,1574,1665,1773,1832,1897,1959,2022,2084,2165,2228,2306,2372,2429,2484,2537,2622,2706,2840,2930,3034,3111,3181,3263,3335,3394,3456,3516,3599,3662,3745,3817,3890,3968,4041,4120,4184,4267,4343,4406,4468,4534,4591,4636,4683,18528,22985,23167,23217,23454,23519,23753,23888,25037", "endColumns": "47,63,45,51,44,46,47,49,49,50,47,50,51,40,43,51,51,53,68,75,69,68,59,59,62,45,60,90,107,58,64,61,62,61,80,62,77,65,56,54,52,84,83,133,89,103,76,69,81,71,58,61,59,82,62,82,71,72,77,72,78,63,82,75,62,61,65,56,44,46,48,53,46,49,64,64,66,49,39,50", "endOffsets": "148,212,258,310,355,402,450,500,550,601,649,700,752,793,837,889,941,995,1064,1140,1210,1279,1339,1399,1462,1508,1569,1660,1768,1827,1892,1954,2017,2079,2160,2223,2301,2367,2424,2479,2532,2617,2701,2835,2925,3029,3106,3176,3258,3330,3389,3451,3511,3594,3657,3740,3812,3885,3963,4036,4115,4179,4262,4338,4401,4463,4529,4586,4631,4678,4727,18577,23027,23212,23277,23514,23581,23798,23923,25083"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\4f725fd807947c49350c5a696b32f0e9\\transformed\\preference-1.2.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,174,261,341,476,645,725", "endColumns": "68,86,79,134,168,79,75", "endOffsets": "169,256,336,471,640,720,796"}, "to": {"startLines": "110,141,246,248,264,270,271", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "8450,11106,22905,23032,24247,24881,24961", "endColumns": "68,86,79,134,168,79,75", "endOffsets": "8514,11188,22980,23162,24411,24956,25032"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\b12f5ec7860c79fe319ea01d980e4aac\\transformed\\ui-release\\res\\values-it\\values-it.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "196,296,383,481,581,668,747,853,946,1041,1125,1213,1298,1383,1459,1531,1601,1679,1748", "endColumns": "99,86,97,99,86,78,105,92,94,83,87,84,84,75,71,69,77,68,120", "endOffsets": "291,378,476,576,663,742,848,941,1036,1120,1208,1293,1378,1454,1526,1596,1674,1743,1864"}, "to": {"startLines": "108,109,111,113,114,143,144,244,245,251,252,256,258,260,261,262,265,266,267", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8263,8363,8519,8699,8799,11325,11404,22717,22810,23282,23366,23668,23803,23928,24004,24076,24416,24494,24563", "endColumns": "99,86,97,99,86,78,105,92,94,83,87,84,84,75,71,69,77,68,120", "endOffsets": "8358,8445,8612,8794,8881,11399,11505,22805,22900,23361,23449,23748,23883,23999,24071,24141,24489,24558,24679"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\2fbbcb27eaccaf382f754b7d51fec492\\transformed\\mediarouter-1.7.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,187,285,388,495,604,733,855,957,1044,1222,1407,1600,1790,1972,2168,2359,2483,2587,2708,2805,2901,2996,3089,3211,3343,3435,3522,3610,3718,3823,3917,4026,4137,4224", "endColumns": "131,97,102,106,108,128,121,101,86,177,184,192,189,181,195,190,123,103,120,96,95,94,92,121,131,91,86,87,107,104,93,108,110,86,97", "endOffsets": "182,280,383,490,599,728,850,952,1039,1217,1402,1595,1785,1967,2163,2354,2478,2582,2703,2800,2896,2991,3084,3206,3338,3430,3517,3605,3713,3818,3912,4021,4132,4219,4317"}, "to": {"startLines": "142,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "11193,18582,18680,18783,18890,18999,19128,19250,19352,19439,19617,19802,19995,20185,20367,20563,20754,20878,20982,21103,21200,21296,21391,21484,21606,21738,21830,21917,22005,22113,22218,22312,22421,22532,22619", "endColumns": "131,97,102,106,108,128,121,101,86,177,184,192,189,181,195,190,123,103,120,96,95,94,92,121,131,91,86,87,107,104,93,108,110,86,97", "endOffsets": "11320,18675,18778,18885,18994,19123,19245,19347,19434,19612,19797,19990,20180,20362,20558,20749,20873,20977,21098,21195,21291,21386,21479,21601,21733,21825,21912,22000,22108,22213,22307,22416,22527,22614,22712"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\64a01e76fd68fe16459fb0e17d50396b\\transformed\\appcompat-1.6.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,313,422,506,611,730,808,883,975,1069,1162,1256,1357,1451,1548,1643,1735,1827,1908,2014,2121,2219,2323,2429,2536,2699,2799", "endColumns": "104,102,108,83,104,118,77,74,91,93,92,93,100,93,96,94,91,91,80,105,106,97,103,105,106,162,99,81", "endOffsets": "205,308,417,501,606,725,803,878,970,1064,1157,1251,1352,1446,1543,1638,1730,1822,1903,2009,2116,2214,2318,2424,2531,2694,2794,2876"}, "to": {"startLines": "73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,255", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4732,4837,4940,5049,5133,5238,5357,5435,5510,5602,5696,5789,5883,5984,6078,6175,6270,6362,6454,6535,6641,6748,6846,6950,7056,7163,7326,23586", "endColumns": "104,102,108,83,104,118,77,74,91,93,92,93,100,93,96,94,91,91,80,105,106,97,103,105,106,162,99,81", "endOffsets": "4832,4935,5044,5128,5233,5352,5430,5505,5597,5691,5784,5878,5979,6073,6170,6265,6357,6449,6530,6636,6743,6841,6945,7051,7158,7321,7421,23663"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\14cf378553861cbecadb791c6e2341a9\\transformed\\core-1.16.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,565,672,802", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "148,250,349,451,560,667,797,898"}, "to": {"startLines": "101,102,103,104,105,106,107,263", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "7516,7614,7716,7815,7917,8026,8133,24146", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "7609,7711,7810,7912,8021,8128,8258,24242"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\886fef481c8ce4a2616ccf22a8cd1328\\transformed\\foundation-release\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,145,243", "endColumns": "89,97,98", "endOffsets": "140,238,337"}, "to": {"startLines": "100,268,269", "startColumns": "4,4,4", "startOffsets": "7426,24684,24782", "endColumns": "89,97,98", "endOffsets": "7511,24777,24876"}}]}]}