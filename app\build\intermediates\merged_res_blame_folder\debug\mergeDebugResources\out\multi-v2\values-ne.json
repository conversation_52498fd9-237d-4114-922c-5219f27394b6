{"logs": [{"outputFile": "com.craftworks.music.app-mergeDebugResources-3:/values-ne/values-ne.xml", "map": [{"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\b12f5ec7860c79fe319ea01d980e4aac\\transformed\\ui-release\\res\\values-ne\\values-ne.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "179,278,368,462,559,645,727,823,910,996,1086,1179,1256,1340,1415,1488,1560,1641,1709", "endColumns": "98,89,93,96,85,81,95,86,85,89,92,76,83,74,72,71,80,67,119", "endOffsets": "273,363,457,554,640,722,818,905,991,1081,1174,1251,1335,1410,1483,1555,1636,1704,1824"}, "to": {"startLines": "37,38,40,42,43,72,73,172,173,176,177,179,180,181,182,183,186,187,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3658,3757,3930,4105,4202,6716,6798,18304,18391,18705,18795,18968,19045,19129,19204,19277,19619,19700,19768", "endColumns": "98,89,93,96,85,81,95,86,85,89,92,76,83,74,72,71,80,67,119", "endOffsets": "3752,3842,4019,4197,4283,6793,6889,18386,18472,18790,18883,19040,19124,19199,19272,19344,19695,19763,19883"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\493af54b1b0004569d1870cf1ba0af14\\transformed\\media3-session-1.7.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,136,237,307,404,498,582,664,754,857,946,1020,1121,1208,1305,1378,1475,1556,1656,1731,1807,1888,1972,2064", "endColumns": "80,100,69,96,93,83,81,89,102,88,73,100,86,96,72,96,80,99,74,75,80,83,91,97", "endOffsets": "131,232,302,399,493,577,659,749,852,941,1015,1116,1203,1300,1373,1470,1551,1651,1726,1802,1883,1967,2059,2157"}, "to": {"startLines": "41,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,132,133,134,135,136,137", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4024,4288,4389,4459,4556,4650,4734,4816,4906,5009,5098,5172,5273,5360,5457,5530,5627,5708,13446,13521,13597,13678,13762,13854", "endColumns": "80,100,69,96,93,83,81,89,102,88,73,100,86,96,72,96,80,99,74,75,80,83,91,97", "endOffsets": "4100,4384,4454,4551,4645,4729,4811,4901,5004,5093,5167,5268,5355,5452,5525,5622,5703,5803,13516,13592,13673,13757,13849,13947"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\114a5d5301d7649ab21683cbabeecc89\\transformed\\media3-exoplayer-1.7.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,197,267,334,412,489,589,683", "endColumns": "70,70,69,66,77,76,99,93,68", "endOffsets": "121,192,262,329,407,484,584,678,747"}, "to": {"startLines": "61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "5808,5879,5950,6020,6087,6165,6242,6342,6436", "endColumns": "70,70,69,66,77,76,99,93,68", "endOffsets": "5874,5945,6015,6082,6160,6237,6337,6431,6500"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\c3709e4bfd5d4e5c1ed8a624d490cd12\\transformed\\material-release\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "87", "endOffsets": "138"}, "to": {"startLines": "131", "startColumns": "4", "startOffsets": "13358", "endColumns": "87", "endOffsets": "13441"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\64a01e76fd68fe16459fb0e17d50396b\\transformed\\appcompat-1.6.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,325,433,524,631,751,835,914,1005,1098,1193,1287,1387,1480,1575,1669,1760,1851,1937,2050,2151,2247,2360,2470,2587,2754,2865", "endColumns": "108,110,107,90,106,119,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,95,112,109,116,166,110,79", "endOffsets": "209,320,428,519,626,746,830,909,1000,1093,1188,1282,1382,1475,1570,1664,1755,1846,1932,2045,2146,2242,2355,2465,2582,2749,2860,2940"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,178", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,325,433,524,631,751,835,914,1005,1098,1193,1287,1387,1480,1575,1669,1760,1851,1937,2050,2151,2247,2360,2470,2587,2754,18888", "endColumns": "108,110,107,90,106,119,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,95,112,109,116,166,110,79", "endOffsets": "209,320,428,519,626,746,830,909,1000,1093,1188,1282,1382,1475,1570,1664,1755,1846,1932,2045,2146,2242,2355,2465,2582,2749,2860,18963"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\886fef481c8ce4a2616ccf22a8cd1328\\transformed\\foundation-release\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,128,213", "endColumns": "72,84,90", "endOffsets": "123,208,299"}, "to": {"startLines": "29,189,190", "startColumns": "4,4,4", "startOffsets": "2865,19888,19973", "endColumns": "72,84,90", "endOffsets": "2933,19968,20059"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\14cf378553861cbecadb791c6e2341a9\\transformed\\core-1.16.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,261,363,469,567,667,775", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "153,256,358,464,562,662,770,871"}, "to": {"startLines": "30,31,32,33,34,35,36,184", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2938,3041,3144,3246,3352,3450,3550,19349", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "3036,3139,3241,3347,3445,3545,3653,19445"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\4f725fd807947c49350c5a696b32f0e9\\transformed\\preference-1.2.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,188,272,353,500,669,767", "endColumns": "82,83,80,146,168,97,79", "endOffsets": "183,267,348,495,664,762,842"}, "to": {"startLines": "39,70,174,175,185,191,192", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3847,6505,18477,18558,19450,20064,20162", "endColumns": "82,83,80,146,168,97,79", "endOffsets": "3925,6584,18553,18700,19614,20157,20237"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\c9938815eb16e1bbe389a6512658438a\\transformed\\material3-release\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,184,303,419,547,646,741,853,1005,1126,1279,1363,1471,1569,1668,1780,1904,2017,2163,2306,2440,2605,2735,2887,3044,3173,3272,3367,3483,3607,3711,3830,3940,4086,4234,4344,4452,4527,4632,4737,4848,4939,5034,5141,5221,5306,5407,5516,5611,5714,5801,5912,6011,6116,6239,6319,6425", "endColumns": "128,118,115,127,98,94,111,151,120,152,83,107,97,98,111,123,112,145,142,133,164,129,151,156,128,98,94,115,123,103,118,109,145,147,109,107,74,104,104,110,90,94,106,79,84,100,108,94,102,86,110,98,104,122,79,105,93", "endOffsets": "179,298,414,542,641,736,848,1000,1121,1274,1358,1466,1564,1663,1775,1899,2012,2158,2301,2435,2600,2730,2882,3039,3168,3267,3362,3478,3602,3706,3825,3935,4081,4229,4339,4447,4522,4627,4732,4843,4934,5029,5136,5216,5301,5402,5511,5606,5709,5796,5907,6006,6111,6234,6314,6420,6514"}, "to": {"startLines": "74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6894,7023,7142,7258,7386,7485,7580,7692,7844,7965,8118,8202,8310,8408,8507,8619,8743,8856,9002,9145,9279,9444,9574,9726,9883,10012,10111,10206,10322,10446,10550,10669,10779,10925,11073,11183,11291,11366,11471,11576,11687,11778,11873,11980,12060,12145,12246,12355,12450,12553,12640,12751,12850,12955,13078,13158,13264", "endColumns": "128,118,115,127,98,94,111,151,120,152,83,107,97,98,111,123,112,145,142,133,164,129,151,156,128,98,94,115,123,103,118,109,145,147,109,107,74,104,104,110,90,94,106,79,84,100,108,94,102,86,110,98,104,122,79,105,93", "endOffsets": "7018,7137,7253,7381,7480,7575,7687,7839,7960,8113,8197,8305,8403,8502,8614,8738,8851,8997,9140,9274,9439,9569,9721,9878,10007,10106,10201,10317,10441,10545,10664,10774,10920,11068,11178,11286,11361,11466,11571,11682,11773,11868,11975,12055,12140,12241,12350,12445,12548,12635,12746,12845,12950,13073,13153,13259,13353"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\2fbbcb27eaccaf382f754b7d51fec492\\transformed\\mediarouter-1.7.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,182,286,399,516,636,760,870,975,1070,1248,1467,1688,1916,2135,2361,2590,2710,2803,2912,3017,3124,3225,3328,3445,3561,3652,3740,3828,3931,4035,4127,4236,4351,4438", "endColumns": "126,103,112,116,119,123,109,104,94,177,218,220,227,218,225,228,119,92,108,104,106,100,102,116,115,90,87,87,102,103,91,108,114,86,95", "endOffsets": "177,281,394,511,631,755,865,970,1065,1243,1462,1683,1911,2130,2356,2585,2705,2798,2907,3012,3119,3220,3323,3440,3556,3647,3735,3823,3926,4030,4122,4231,4346,4433,4529"}, "to": {"startLines": "71,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6589,13952,14056,14169,14286,14406,14530,14640,14745,14840,15018,15237,15458,15686,15905,16131,16360,16480,16573,16682,16787,16894,16995,17098,17215,17331,17422,17510,17598,17701,17805,17897,18006,18121,18208", "endColumns": "126,103,112,116,119,123,109,104,94,177,218,220,227,218,225,228,119,92,108,104,106,100,102,116,115,90,87,87,102,103,91,108,114,86,95", "endOffsets": "6711,14051,14164,14281,14401,14525,14635,14740,14835,15013,15232,15453,15681,15900,16126,16355,16475,16568,16677,16782,16889,16990,17093,17210,17326,17417,17505,17593,17696,17800,17892,18001,18116,18203,18299"}}]}]}