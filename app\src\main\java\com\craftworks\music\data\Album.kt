package com.craftworks.music.data

import android.os.Bundle
import androidx.compose.runtime.mutableStateListOf
import androidx.core.net.toUri
import androidx.media3.common.MediaItem
import androidx.media3.common.MediaMetadata

var albumList:MutableList<MediaData.Album> = mutableStateListOf()

fun MediaData.Album.toMediaItem(): MediaItem {
    val mediaMetadata = MediaMetadata.Builder()
        .setTitle(<EMAIL>)
        .setArtist(<EMAIL>)
        .setAlbumTitle(<EMAIL>)
        .setDisplayTitle(<EMAIL>)
        .setAlbumArtist(<EMAIL>)
        .setArtworkUri(<EMAIL>?.toUri())
        .setRecordingYear(<EMAIL>)
        .setDurationMs(<EMAIL>(1000).toLong())
        .setIsBrowsable(true)
        .setIsPlayable(false)
        .setGenre(<EMAIL>?.joinToString() { it.name ?: "" })
        .setMediaType(MediaMetadata.MEDIA_TYPE_ALBUM)
        .setExtras(
            Bundle().apply {
                putString("navidromeID", <EMAIL>)
            }
        )
        .build()

    return MediaItem.Builder()
        .setMediaId(
            if (<EMAIL>("Local_"))
                "folder_album_" + <EMAIL>
            else
                <EMAIL>
        )
        .setMediaMetadata(mediaMetadata)
        .build()
}

fun MediaItem.toAlbum(): MediaData.Album {
    val mediaMetadata = this.mediaMetadata
    val extras = mediaMetadata.extras

    return MediaData.Album(
        navidromeID = extras?.getString("navidromeID") ?: "",
        name = mediaMetadata.albumTitle.toString(),
        artist = mediaMetadata.artist.toString(),
        year = mediaMetadata.releaseYear ?: 0,
        coverArt = mediaMetadata.artworkUri.toString(),
        duration = extras?.getInt("Duration") ?: 0,
        songs = mutableListOf(),
        songCount = 0,
        artistId = ""
    )
}