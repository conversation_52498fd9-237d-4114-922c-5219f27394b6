@file:OptIn(UnstableApi::class) package com.craftworks.music.player

import android.util.Log
import androidx.annotation.OptIn
import androidx.compose.runtime.mutableIntStateOf
import androidx.media3.common.MediaItem
import androidx.media3.common.util.UnstableApi
import androidx.media3.session.MediaController

class SongHelper {
    companion object{
        var currentTracklist = mutableListOf<MediaItem>()
        var minPercentageScrobble = mutableIntStateOf(75)
        var currentMediaController: MediaController? = null

        fun play(mediaItems: List<MediaItem>, index: Int, mediaController: MediaController?) {
            Log.d("SONG_HELPER", "=== PLAY CALLED ===")
            Log.d("SONG_HELPER", "play() called with ${mediaItems.size} items, index: $index")
            Log.d("SONG_HELPER", "MediaController: $mediaController")
            Log.d("SONG_HELPER", "MediaController connected: ${mediaController?.isConnected}")
            Log.d("SONG_HELPER", "currentMediaController: $currentMediaController")

            // Log first few items for debugging
            mediaItems.take(3).forEachIndexed { i, item ->
                Log.d("SONG_HELPER", "Item $i: ${item.mediaMetadata.title} (Type: ${item.mediaMetadata.mediaType})")
                Log.d("SONG_HELPER", "Item $i MediaId: ${item.mediaId}")
                Log.d("SONG_HELPER", "Item $i URI: ${item.localConfiguration?.uri}")
            }

            // Try to use any available MediaController
            val effectiveController = mediaController ?: currentMediaController

            if (effectiveController == null) {
                Log.e("SONG_HELPER", "No MediaController available (passed: $mediaController, cached: $currentMediaController)")

                // Try to get MediaController directly from service
                val serviceController = ChoraMediaLibraryService.getInstance()?.player
                if (serviceController != null) {
                    Log.w("SONG_HELPER", "Using player directly from service")
                    playDirectly(mediaItems, index, serviceController)
                    return
                } else {
                    Log.e("SONG_HELPER", "Service player is also null, cannot play")
                    return
                }
            }

            Log.d("SONG_HELPER", "Using effective controller: $effectiveController")

            if (!effectiveController.isConnected) {
                Log.e("SONG_HELPER", "MediaController is not connected, cannot play")
                return
            }

            if (mediaItems.isEmpty()) {
                Log.e("SONG_HELPER", "MediaItems list is empty, cannot play")
                return
            }

            if (index < 0 || index >= mediaItems.size) {
                Log.e("SONG_HELPER", "Invalid index: $index for list size: ${mediaItems.size}")
                return
            }

            // Validate MediaItem
            val targetItem = mediaItems[index]
            if (targetItem.mediaId.isEmpty()) {
                Log.e("SONG_HELPER", "MediaItem has empty mediaId")
                return
            }

            try {
                // Step 1: 先设置播放列表
                currentTracklist = (mediaItems).toMutableList()
                currentMediaController = effectiveController

                Log.d("SONG_HELPER", "=== STEP 1: PREPARING PLAYLIST ===")
                Log.d("SONG_HELPER", "Setting ${mediaItems.size} media items")
                Log.d("SONG_HELPER", "Target song: ${targetItem.mediaMetadata.title}")
                Log.d("SONG_HELPER", "Target index: $index")

                // Double check connection before operations
                if (!effectiveController.isConnected) {
                    Log.e("SONG_HELPER", "MediaController disconnected during setup")
                    return
                }

                // Step 2: 设置播放列表但不自动播放
                Log.d("SONG_HELPER", "=== STEP 2: SETTING MEDIA ITEMS ===")
                effectiveController.setMediaItems(currentTracklist, index, 0)
                Log.d("SONG_HELPER", "Media items set successfully")

                // Step 3: 准备播放器
                Log.d("SONG_HELPER", "=== STEP 3: PREPARING PLAYER ===")
                effectiveController.prepare()
                Log.d("SONG_HELPER", "MediaController prepared")

                // Step 4: 等待准备完成再播放
                Log.d("SONG_HELPER", "=== STEP 4: WAITING FOR READY STATE ===")
                var waitCount = 0
                while (effectiveController.playbackState != androidx.media3.common.Player.STATE_READY &&
                       effectiveController.playbackState != androidx.media3.common.Player.STATE_BUFFERING &&
                       waitCount < 50) {
                    Thread.sleep(100)
                    waitCount++
                    Log.d("SONG_HELPER", "Waiting for ready state... ${effectiveController.playbackState}")
                }

                if (effectiveController.playbackState == androidx.media3.common.Player.STATE_READY ||
                    effectiveController.playbackState == androidx.media3.common.Player.STATE_BUFFERING) {
                    Log.d("SONG_HELPER", "=== STEP 5: STARTING PLAYBACK ===")
                    effectiveController.play()
                    Log.d("SONG_HELPER", "Play command sent")

                    // 等待播放开始
                    Thread.sleep(200)
                    Log.d("SONG_HELPER", "Final player state: ${effectiveController.playbackState}")
                    Log.d("SONG_HELPER", "Final isPlaying: ${effectiveController.isPlaying}")
                } else {
                    Log.e("SONG_HELPER", "Player not ready after waiting, state: ${effectiveController.playbackState}")
                }

                Log.d("SONG_HELPER", "Playback setup completed")

                // Save state immediately when starting new playback
                ChoraMediaLibraryService.getInstance()?.saveState()
            } catch (e: Exception) {
                Log.e("SONG_HELPER", "Error during playback: ${e.message}", e)
                e.printStackTrace()
            }
        }

        // Direct playback using ExoPlayer when MediaController is not available
        private fun playDirectly(mediaItems: List<MediaItem>, index: Int, player: androidx.media3.common.Player) {
            Log.d("SONG_HELPER", "=== DIRECT PLAY ===")
            try {
                currentTracklist = mediaItems.toMutableList()

                Log.d("SONG_HELPER", "=== DIRECT STEP 1: SETTING MEDIA ITEMS ===")
                player.setMediaItems(currentTracklist, index, 0)
                Log.d("SONG_HELPER", "Media items set on player")

                Log.d("SONG_HELPER", "=== DIRECT STEP 2: PREPARING ===")
                player.prepare()
                Log.d("SONG_HELPER", "Player prepared")

                Log.d("SONG_HELPER", "=== DIRECT STEP 3: WAITING FOR READY ===")
                var waitCount = 0
                while (player.playbackState != androidx.media3.common.Player.STATE_READY &&
                       player.playbackState != androidx.media3.common.Player.STATE_BUFFERING &&
                       waitCount < 50) {
                    Thread.sleep(100)
                    waitCount++
                    Log.d("SONG_HELPER", "Direct waiting for ready... ${player.playbackState}")
                }

                if (player.playbackState == androidx.media3.common.Player.STATE_READY ||
                    player.playbackState == androidx.media3.common.Player.STATE_BUFFERING) {
                    Log.d("SONG_HELPER", "=== DIRECT STEP 4: STARTING PLAYBACK ===")
                    player.play()
                    Log.d("SONG_HELPER", "Direct play command sent")

                    Thread.sleep(200)
                    Log.d("SONG_HELPER", "Direct final state: ${player.playbackState}")
                    Log.d("SONG_HELPER", "Direct final isPlaying: ${player.isPlaying}")
                } else {
                    Log.e("SONG_HELPER", "Direct player not ready, state: ${player.playbackState}")
                }

                Log.d("SONG_HELPER", "Direct playback setup completed")

                // Manually trigger metadata update for UI
                val currentItem = mediaItems[index]
                Log.d("SONG_HELPER", "Manually updating metadata for: ${currentItem.mediaMetadata.title}")

                // Force service to start foreground and update notifications
                ChoraMediaLibraryService.getInstance()?.let { service ->
                    Log.d("SONG_HELPER", "Service available, triggering foreground service")
                    // The service player listeners should handle this automatically
                    // but let's make sure by checking if it's playing
                    if (player.isPlaying) {
                        Log.d("SONG_HELPER", "Player is playing, service should start foreground")
                    }
                }

            } catch (e: Exception) {
                Log.e("SONG_HELPER", "Error during direct playback: ${e.message}", e)
            }
        }
    }
}