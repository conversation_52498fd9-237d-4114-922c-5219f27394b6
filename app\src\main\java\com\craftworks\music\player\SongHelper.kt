@file:OptIn(UnstableApi::class) package com.craftworks.music.player

import android.util.Log
import androidx.annotation.OptIn
import androidx.compose.runtime.mutableIntStateOf
import androidx.media3.common.MediaItem
import androidx.media3.common.util.UnstableApi
import androidx.media3.session.MediaController

class SongHelper {
    companion object{
        var currentTracklist = mutableListOf<MediaItem>()
        var minPercentageScrobble = mutableIntStateOf(75)
        var currentMediaController: MediaController? = null

        fun play(mediaItems: List<MediaItem>, index: Int, mediaController: MediaController?) {
            Log.d("SONG_HELPER", "=== PLAY CALLED ===")
            Log.d("SONG_HELPER", "play() called with ${mediaItems.size} items, index: $index")
            Log.d("SONG_HELPER", "MediaController: $mediaController")
            Log.d("SONG_HELPER", "MediaController connected: ${mediaController?.isConnected}")

            // Log first few items for debugging
            mediaItems.take(3).forEachIndexed { i, item ->
                Log.d("SONG_HELPER", "Item $i: ${item.mediaMetadata.title} (Type: ${item.mediaMetadata.mediaType})")
                Log.d("SONG_HELPER", "Item $i MediaId: ${item.mediaId}")
                Log.d("SONG_HELPER", "Item $i URI: ${item.localConfiguration?.uri}")
            }

            if (mediaController == null) {
                Log.e("SONG_HELPER", "MediaController is null, cannot play")
                return
            }

            if (!mediaController.isConnected) {
                Log.e("SONG_HELPER", "MediaController is not connected, cannot play")
                return
            }

            if (mediaItems.isEmpty()) {
                Log.e("SONG_HELPER", "MediaItems list is empty, cannot play")
                return
            }

            if (index < 0 || index >= mediaItems.size) {
                Log.e("SONG_HELPER", "Invalid index: $index for list size: ${mediaItems.size}")
                return
            }

            // Validate MediaItem
            val targetItem = mediaItems[index]
            if (targetItem.mediaId.isEmpty()) {
                Log.e("SONG_HELPER", "MediaItem has empty mediaId")
                return
            }

            try {
                currentTracklist = (mediaItems).toMutableList()
                currentMediaController = mediaController

                Log.d("SONG_HELPER", "Setting media items and starting playback from index $index")
                Log.d("SONG_HELPER", "Starting song: ${targetItem.mediaMetadata.title}")
                Log.d("SONG_HELPER", "Song MediaId: ${targetItem.mediaId}")
                Log.d("SONG_HELPER", "Song URI: ${targetItem.localConfiguration?.uri}")

                // Double check connection before operations
                if (!mediaController.isConnected) {
                    Log.e("SONG_HELPER", "MediaController disconnected during setup")
                    return
                }

                Log.d("SONG_HELPER", "Calling setMediaItems...")
                mediaController.setMediaItems(currentTracklist, index, 0)
                Log.d("SONG_HELPER", "Media items set successfully")

                Log.d("SONG_HELPER", "Calling prepare...")
                mediaController.prepare()
                Log.d("SONG_HELPER", "MediaController prepared")

                Log.d("SONG_HELPER", "Calling play...")
                mediaController.play()
                Log.d("SONG_HELPER", "Play command sent")

                // Check player state after play command
                Log.d("SONG_HELPER", "Player state after play: ${mediaController.playbackState}")
                Log.d("SONG_HELPER", "Player isPlaying: ${mediaController.isPlaying}")

                Log.d("SONG_HELPER", "Playback started successfully")

                // Save state immediately when starting new playback
                ChoraMediaLibraryService.getInstance()?.saveState()
            } catch (e: Exception) {
                Log.e("SONG_HELPER", "Error during playback: ${e.message}", e)
                e.printStackTrace()
            }
        }
    }
}