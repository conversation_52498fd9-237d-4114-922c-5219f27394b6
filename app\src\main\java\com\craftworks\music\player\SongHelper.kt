@file:OptIn(UnstableApi::class) package com.craftworks.music.player

import android.util.Log
import androidx.annotation.OptIn
import androidx.compose.runtime.mutableIntStateOf
import androidx.media3.common.MediaItem
import androidx.media3.common.util.UnstableApi
import androidx.media3.session.MediaController

class SongHelper {
    companion object{
        var currentTracklist = mutableListOf<MediaItem>()
        var minPercentageScrobble = mutableIntStateOf(75)
        var currentMediaController: MediaController? = null

        fun play(mediaItems: List<MediaItem>, index: Int, mediaController: MediaController?) {
            Log.d("SONG_HELPER", "play() called with ${mediaItems.size} items, index: $index")

            // Log first few items for debugging
            mediaItems.take(3).forEachIndexed { i, item ->
                Log.d("SONG_HELPER", "Item $i: ${item.mediaMetadata.title} (Type: ${item.mediaMetadata.mediaType})")
            }

            if (mediaController == null) {
                Log.e("SONG_HELPER", "MediaController is null, cannot play")
                return
            }

            if (mediaItems.isEmpty()) {
                Log.e("SONG_HELPER", "MediaItems list is empty, cannot play")
                return
            }

            if (index < 0 || index >= mediaItems.size) {
                Log.e("SONG_HELPER", "Invalid index: $index for list size: ${mediaItems.size}")
                return
            }

            try {
                currentTracklist = (mediaItems).toMutableList()
                currentMediaController = mediaController

                Log.d("SONG_HELPER", "Setting media items and starting playback from index $index")
                Log.d("SONG_HELPER", "Starting song: ${mediaItems[index].mediaMetadata.title}")

                mediaController.setMediaItems(currentTracklist, index, 0)
                mediaController.prepare()
                mediaController.play()

                Log.d("SONG_HELPER", "Playback started successfully")

                // Save state immediately when starting new playback
                ChoraMediaLibraryService.getInstance()?.saveState()
            } catch (e: Exception) {
                Log.e("SONG_HELPER", "Error during playback: ${e.message}", e)
            }
        }
    }
}