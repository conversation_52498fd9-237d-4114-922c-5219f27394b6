{"logs": [{"outputFile": "com.craftworks.music.app-mergeDebugResources-3:/values-te/values-te.xml", "map": [{"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\b12f5ec7860c79fe319ea01d980e4aac\\transformed\\ui-release\\res\\values-te\\values-te.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "180,278,367,464,564,653,742,838,926,1010,1094,1184,1261,1348,1430,1510,1589,1666,1735", "endColumns": "97,88,96,99,88,88,95,87,83,83,89,76,86,81,79,78,76,68,116", "endOffsets": "273,362,459,559,648,737,833,921,1005,1089,1179,1256,1343,1425,1505,1584,1661,1730,1847"}, "to": {"startLines": "37,38,40,42,43,72,73,172,173,176,177,179,180,181,182,183,186,187,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3683,3781,3943,4128,4228,6791,6880,18055,18143,18452,18536,18709,18786,18873,18955,19035,19384,19461,19530", "endColumns": "97,88,96,99,88,88,95,87,83,83,89,76,86,81,79,78,76,68,116", "endOffsets": "3776,3865,4035,4223,4312,6875,6971,18138,18222,18531,18621,18781,18868,18950,19030,19109,19456,19525,19642"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\2fbbcb27eaccaf382f754b7d51fec492\\transformed\\mediarouter-1.7.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,184,287,401,518,640,760,877,978,1075,1257,1426,1597,1773,1941,2116,2287,2409,2504,2621,2720,2815,2916,3012,3125,3237,3324,3411,3492,3596,3700,3794,3900,4010,4097", "endColumns": "128,102,113,116,121,119,116,100,96,181,168,170,175,167,174,170,121,94,116,98,94,100,95,112,111,86,86,80,103,103,93,105,109,86,94", "endOffsets": "179,282,396,513,635,755,872,973,1070,1252,1421,1592,1768,1936,2111,2282,2404,2499,2616,2715,2810,2911,3007,3120,3232,3319,3406,3487,3591,3695,3789,3895,4005,4092,4187"}, "to": {"startLines": "71,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6662,14047,14150,14264,14381,14503,14623,14740,14841,14938,15120,15289,15460,15636,15804,15979,16150,16272,16367,16484,16583,16678,16779,16875,16988,17100,17187,17274,17355,17459,17563,17657,17763,17873,17960", "endColumns": "128,102,113,116,121,119,116,100,96,181,168,170,175,167,174,170,121,94,116,98,94,100,95,112,111,86,86,80,103,103,93,105,109,86,94", "endOffsets": "6786,14145,14259,14376,14498,14618,14735,14836,14933,15115,15284,15455,15631,15799,15974,16145,16267,16362,16479,16578,16673,16774,16870,16983,17095,17182,17269,17350,17454,17558,17652,17758,17868,17955,18050"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\4f725fd807947c49350c5a696b32f0e9\\transformed\\preference-1.2.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,178,277,356,502,671,758", "endColumns": "72,98,78,145,168,86,83", "endOffsets": "173,272,351,497,666,753,837"}, "to": {"startLines": "39,70,174,175,185,191,192", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3870,6563,18227,18306,19215,19830,19917", "endColumns": "72,98,78,145,168,86,83", "endOffsets": "3938,6657,18301,18447,19379,19912,19996"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\c9938815eb16e1bbe389a6512658438a\\transformed\\material3-release\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,184,315,429,559,663,762,878,1019,1131,1274,1358,1461,1557,1655,1771,1901,2009,2158,2305,2438,2634,2762,2878,2999,3136,3233,3330,3455,3583,3689,3795,3901,4044,4194,4302,4406,4482,4581,4682,4798,4892,4984,5091,5171,5254,5355,5483,5577,5689,5777,5888,5990,6107,6230,6310,6417", "endColumns": "128,130,113,129,103,98,115,140,111,142,83,102,95,97,115,129,107,148,146,132,195,127,115,120,136,96,96,124,127,105,105,105,142,149,107,103,75,98,100,115,93,91,106,79,82,100,127,93,111,87,110,101,116,122,79,106,96", "endOffsets": "179,310,424,554,658,757,873,1014,1126,1269,1353,1456,1552,1650,1766,1896,2004,2153,2300,2433,2629,2757,2873,2994,3131,3228,3325,3450,3578,3684,3790,3896,4039,4189,4297,4401,4477,4576,4677,4793,4887,4979,5086,5166,5249,5350,5478,5572,5684,5772,5883,5985,6102,6225,6305,6412,6509"}, "to": {"startLines": "74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6976,7105,7236,7350,7480,7584,7683,7799,7940,8052,8195,8279,8382,8478,8576,8692,8822,8930,9079,9226,9359,9555,9683,9799,9920,10057,10154,10251,10376,10504,10610,10716,10822,10965,11115,11223,11327,11403,11502,11603,11719,11813,11905,12012,12092,12175,12276,12404,12498,12610,12698,12809,12911,13028,13151,13231,13338", "endColumns": "128,130,113,129,103,98,115,140,111,142,83,102,95,97,115,129,107,148,146,132,195,127,115,120,136,96,96,124,127,105,105,105,142,149,107,103,75,98,100,115,93,91,106,79,82,100,127,93,111,87,110,101,116,122,79,106,96", "endOffsets": "7100,7231,7345,7475,7579,7678,7794,7935,8047,8190,8274,8377,8473,8571,8687,8817,8925,9074,9221,9354,9550,9678,9794,9915,10052,10149,10246,10371,10499,10605,10711,10817,10960,11110,11218,11322,11398,11497,11598,11714,11808,11900,12007,12087,12170,12271,12399,12493,12605,12693,12804,12906,13023,13146,13226,13333,13430"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\114a5d5301d7649ab21683cbabeecc89\\transformed\\media3-exoplayer-1.7.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,195,268,336,416,493,594,687", "endColumns": "71,67,72,67,79,76,100,92,77", "endOffsets": "122,190,263,331,411,488,589,682,760"}, "to": {"startLines": "61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "5853,5925,5993,6066,6134,6214,6291,6392,6485", "endColumns": "71,67,72,67,79,76,100,92,77", "endOffsets": "5920,5988,6061,6129,6209,6286,6387,6480,6558"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\493af54b1b0004569d1870cf1ba0af14\\transformed\\media3-session-1.7.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,143,249,328,420,519,612,689,780,882,971,1043,1151,1229,1332,1419,1509,1578,1679,1753,1826,1910,2004,2100", "endColumns": "87,105,78,91,98,92,76,90,101,88,71,107,77,102,86,89,68,100,73,72,83,93,95,99", "endOffsets": "138,244,323,415,514,607,684,775,877,966,1038,1146,1224,1327,1414,1504,1573,1674,1748,1821,1905,1999,2095,2195"}, "to": {"startLines": "41,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,132,133,134,135,136,137", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4040,4317,4423,4502,4594,4693,4786,4863,4954,5056,5145,5217,5325,5403,5506,5593,5683,5752,13526,13600,13673,13757,13851,13947", "endColumns": "87,105,78,91,98,92,76,90,101,88,71,107,77,102,86,89,68,100,73,72,83,93,95,99", "endOffsets": "4123,4418,4497,4589,4688,4781,4858,4949,5051,5140,5212,5320,5398,5501,5588,5678,5747,5848,13595,13668,13752,13846,13942,14042"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\c3709e4bfd5d4e5c1ed8a624d490cd12\\transformed\\material-release\\res\\values-te\\values-te.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "90", "endOffsets": "141"}, "to": {"startLines": "131", "startColumns": "4", "startOffsets": "13435", "endColumns": "90", "endOffsets": "13521"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\64a01e76fd68fe16459fb0e17d50396b\\transformed\\appcompat-1.6.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,222,334,447,537,642,761,839,915,1006,1099,1194,1288,1388,1481,1576,1671,1762,1853,1942,2056,2160,2259,2374,2479,2594,2756,2859", "endColumns": "116,111,112,89,104,118,77,75,90,92,94,93,99,92,94,94,90,90,88,113,103,98,114,104,114,161,102,82", "endOffsets": "217,329,442,532,637,756,834,910,1001,1094,1189,1283,1383,1476,1571,1666,1757,1848,1937,2051,2155,2254,2369,2474,2589,2751,2854,2937"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,178", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,222,334,447,537,642,761,839,915,1006,1099,1194,1288,1388,1481,1576,1671,1762,1853,1942,2056,2160,2259,2374,2479,2594,2756,18626", "endColumns": "116,111,112,89,104,118,77,75,90,92,94,93,99,92,94,94,90,90,88,113,103,98,114,104,114,161,102,82", "endOffsets": "217,329,442,532,637,756,834,910,1001,1094,1189,1283,1383,1476,1571,1666,1757,1848,1937,2051,2155,2254,2369,2474,2589,2751,2854,18704"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\14cf378553861cbecadb791c6e2341a9\\transformed\\core-1.16.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,265,367,468,574,681,805", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "152,260,362,463,569,676,800,901"}, "to": {"startLines": "30,31,32,33,34,35,36,184", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2933,3035,3143,3245,3346,3452,3559,19114", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "3030,3138,3240,3341,3447,3554,3678,19210"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\886fef481c8ce4a2616ccf22a8cd1328\\transformed\\foundation-release\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,129,217", "endColumns": "73,87,94", "endOffsets": "124,212,307"}, "to": {"startLines": "29,189,190", "startColumns": "4,4,4", "startOffsets": "2859,19647,19735", "endColumns": "73,87,94", "endOffsets": "2928,19730,19825"}}]}]}