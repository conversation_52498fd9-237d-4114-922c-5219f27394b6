{"logs": [{"outputFile": "com.craftworks.music.app-mergeDebugResources-3:/values-fa/values-fa.xml", "map": [{"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\c9938815eb16e1bbe389a6512658438a\\transformed\\material3-release\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,280,394,512,608,704,818,957,1073,1208,1293,1396,1488,1585,1699,1822,1930,2063,2194,2316,2481,2603,2716,2832,2949,3042,3140,3261,3393,3500,3603,3708,3839,3975,4081,4191,4271,4364,4461,4582,4668,4752,4851,4933,5017,5118,5219,5316,5416,5503,5607,5707,5810,5930,6012,6116", "endColumns": "114,109,113,117,95,95,113,138,115,134,84,102,91,96,113,122,107,132,130,121,164,121,112,115,116,92,97,120,131,106,102,104,130,135,105,109,79,92,96,120,85,83,98,81,83,100,100,96,99,86,103,99,102,119,81,103,97", "endOffsets": "165,275,389,507,603,699,813,952,1068,1203,1288,1391,1483,1580,1694,1817,1925,2058,2189,2311,2476,2598,2711,2827,2944,3037,3135,3256,3388,3495,3598,3703,3834,3970,4076,4186,4266,4359,4456,4577,4663,4747,4846,4928,5012,5113,5214,5311,5411,5498,5602,5702,5805,5925,6007,6111,6209"}, "to": {"startLines": "74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6742,6857,6967,7081,7199,7295,7391,7505,7644,7760,7895,7980,8083,8175,8272,8386,8509,8617,8750,8881,9003,9168,9290,9403,9519,9636,9729,9827,9948,10080,10187,10290,10395,10526,10662,10768,10878,10958,11051,11148,11269,11355,11439,11538,11620,11704,11805,11906,12003,12103,12190,12294,12394,12497,12617,12699,12803", "endColumns": "114,109,113,117,95,95,113,138,115,134,84,102,91,96,113,122,107,132,130,121,164,121,112,115,116,92,97,120,131,106,102,104,130,135,105,109,79,92,96,120,85,83,98,81,83,100,100,96,99,86,103,99,102,119,81,103,97", "endOffsets": "6852,6962,7076,7194,7290,7386,7500,7639,7755,7890,7975,8078,8170,8267,8381,8504,8612,8745,8876,8998,9163,9285,9398,9514,9631,9724,9822,9943,10075,10182,10285,10390,10521,10657,10763,10873,10953,11046,11143,11264,11350,11434,11533,11615,11699,11800,11901,11998,12098,12185,12289,12389,12492,12612,12694,12798,12896"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\493af54b1b0004569d1870cf1ba0af14\\transformed\\media3-session-1.7.1\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,239,317,420,516,600,688,772,860,941,1006,1114,1212,1306,1379,1468,1550,1644,1710,1775,1853,1934,2021", "endColumns": "71,111,77,102,95,83,87,83,87,80,64,107,97,93,72,88,81,93,65,64,77,80,86,90", "endOffsets": "122,234,312,415,511,595,683,767,855,936,1001,1109,1207,1301,1374,1463,1545,1639,1705,1770,1848,1929,2016,2107"}, "to": {"startLines": "41,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,132,133,134,135,136,137", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3925,4181,4293,4371,4474,4570,4654,4742,4826,4914,4995,5060,5168,5266,5360,5433,5522,5604,12989,13055,13120,13198,13279,13366", "endColumns": "71,111,77,102,95,83,87,83,87,80,64,107,97,93,72,88,81,93,65,64,77,80,86,90", "endOffsets": "3992,4288,4366,4469,4565,4649,4737,4821,4909,4990,5055,5163,5261,5355,5428,5517,5599,5693,13050,13115,13193,13274,13361,13452"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\114a5d5301d7649ab21683cbabeecc89\\transformed\\media3-exoplayer-1.7.1\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,122,183,250,310,388,463,552,640", "endColumns": "66,60,66,59,77,74,88,87,64", "endOffsets": "117,178,245,305,383,458,547,635,700"}, "to": {"startLines": "61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "5698,5765,5826,5893,5953,6031,6106,6195,6283", "endColumns": "66,60,66,59,77,74,88,87,64", "endOffsets": "5760,5821,5888,5948,6026,6101,6190,6278,6343"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\c3709e4bfd5d4e5c1ed8a624d490cd12\\transformed\\material-release\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "87", "endOffsets": "138"}, "to": {"startLines": "131", "startColumns": "4", "startOffsets": "12901", "endColumns": "87", "endOffsets": "12984"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\4f725fd807947c49350c5a696b32f0e9\\transformed\\preference-1.2.1\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,172,258,335,467,636,718", "endColumns": "66,85,76,131,168,81,77", "endOffsets": "167,253,330,462,631,713,791"}, "to": {"startLines": "39,70,174,175,185,191,192", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3764,6348,17510,17587,18444,19050,19132", "endColumns": "66,85,76,131,168,81,77", "endOffsets": "3826,6429,17582,17714,18608,19127,19205"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\b12f5ec7860c79fe319ea01d980e4aac\\transformed\\ui-release\\res\\values-fa\\values-fa.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "185,272,351,445,543,629,711,814,899,982,1063,1145,1219,1303,1378,1452,1524,1599,1666", "endColumns": "86,78,93,97,85,81,102,84,82,80,81,73,83,74,73,71,74,66,116", "endOffsets": "267,346,440,538,624,706,809,894,977,1058,1140,1214,1298,1373,1447,1519,1594,1661,1778"}, "to": {"startLines": "37,38,40,42,43,72,73,172,173,176,177,179,180,181,182,183,186,187,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3598,3685,3831,3997,4095,6557,6639,17342,17427,17719,17800,17964,18038,18122,18197,18271,18613,18688,18755", "endColumns": "86,78,93,97,85,81,102,84,82,80,81,73,83,74,73,71,74,66,116", "endOffsets": "3680,3759,3920,4090,4176,6634,6737,17422,17505,17795,17877,18033,18117,18192,18266,18338,18683,18750,18867"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\2fbbcb27eaccaf382f754b7d51fec492\\transformed\\mediarouter-1.7.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,178,276,379,489,596,715,828,933,1021,1198,1364,1531,1706,1874,2045,2212,2324,2416,2531,2626,2723,2816,2911,3023,3138,3219,3300,3388,3487,3590,3682,3781,3882,3967", "endColumns": "122,97,102,109,106,118,112,104,87,176,165,166,174,167,170,166,111,91,114,94,96,92,94,111,114,80,80,87,98,102,91,98,100,84,95", "endOffsets": "173,271,374,484,591,710,823,928,1016,1193,1359,1526,1701,1869,2040,2207,2319,2411,2526,2621,2718,2811,2906,3018,3133,3214,3295,3383,3482,3585,3677,3776,3877,3962,4058"}, "to": {"startLines": "71,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6434,13457,13555,13658,13768,13875,13994,14107,14212,14300,14477,14643,14810,14985,15153,15324,15491,15603,15695,15810,15905,16002,16095,16190,16302,16417,16498,16579,16667,16766,16869,16961,17060,17161,17246", "endColumns": "122,97,102,109,106,118,112,104,87,176,165,166,174,167,170,166,111,91,114,94,96,92,94,111,114,80,80,87,98,102,91,98,100,84,95", "endOffsets": "6552,13550,13653,13763,13870,13989,14102,14207,14295,14472,14638,14805,14980,15148,15319,15486,15598,15690,15805,15900,15997,16090,16185,16297,16412,16493,16574,16662,16761,16864,16956,17055,17156,17241,17337"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\64a01e76fd68fe16459fb0e17d50396b\\transformed\\appcompat-1.6.1\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,316,427,511,612,727,807,884,977,1072,1164,1258,1360,1455,1552,1646,1739,1829,1911,2019,2123,2221,2327,2432,2537,2694,2795", "endColumns": "109,100,110,83,100,114,79,76,92,94,91,93,101,94,96,93,92,89,81,107,103,97,105,104,104,156,100,81", "endOffsets": "210,311,422,506,607,722,802,879,972,1067,1159,1253,1355,1450,1547,1641,1734,1824,1906,2014,2118,2216,2322,2427,2532,2689,2790,2872"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,178", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,316,427,511,612,727,807,884,977,1072,1164,1258,1360,1455,1552,1646,1739,1829,1911,2019,2123,2221,2327,2432,2537,2694,17882", "endColumns": "109,100,110,83,100,114,79,76,92,94,91,93,101,94,96,93,92,89,81,107,103,97,105,104,104,156,100,81", "endOffsets": "210,311,422,506,607,722,802,879,972,1067,1159,1253,1355,1450,1547,1641,1734,1824,1906,2014,2118,2216,2322,2427,2532,2689,2790,17959"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\14cf378553861cbecadb791c6e2341a9\\transformed\\core-1.16.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,355,455,556,662,779", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "149,251,350,450,551,657,774,875"}, "to": {"startLines": "30,31,32,33,34,35,36,184", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2874,2973,3075,3174,3274,3375,3481,18343", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "2968,3070,3169,3269,3370,3476,3593,18439"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\886fef481c8ce4a2616ccf22a8cd1328\\transformed\\foundation-release\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,134,223", "endColumns": "78,88,88", "endOffsets": "129,218,307"}, "to": {"startLines": "29,189,190", "startColumns": "4,4,4", "startOffsets": "2795,18872,18961", "endColumns": "78,88,88", "endOffsets": "2869,18956,19045"}}]}]}