{"logs": [{"outputFile": "com.craftworks.music.app-mergeDebugResources-3:/values-bn/values-bn.xml", "map": [{"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\4f725fd807947c49350c5a696b32f0e9\\transformed\\preference-1.2.1\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,261,341,490,659,740", "endColumns": "71,83,79,148,168,80,77", "endOffsets": "172,256,336,485,654,735,813"}, "to": {"startLines": "39,70,174,175,185,191,192", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3815,6487,18066,18146,19040,19640,19721", "endColumns": "71,83,79,148,168,80,77", "endOffsets": "3882,6566,18141,18290,19204,19716,19794"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\886fef481c8ce4a2616ccf22a8cd1328\\transformed\\foundation-release\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,128,213", "endColumns": "72,84,84", "endOffsets": "123,208,293"}, "to": {"startLines": "29,189,190", "startColumns": "4,4,4", "startOffsets": "2835,19470,19555", "endColumns": "72,84,84", "endOffsets": "2903,19550,19635"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\b12f5ec7860c79fe319ea01d980e4aac\\transformed\\ui-release\\res\\values-bn\\values-bn.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "179,273,357,447,545,631,710,816,903,992,1070,1151,1234,1320,1396,1473,1549,1624,1692", "endColumns": "93,83,89,97,85,78,105,86,88,77,80,82,85,75,76,75,74,67,117", "endOffsets": "268,352,442,540,626,705,811,898,987,1065,1146,1229,1315,1391,1468,1544,1619,1687,1805"}, "to": {"startLines": "37,38,40,42,43,72,73,172,173,176,177,179,180,181,182,183,186,187,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3637,3731,3887,4048,4146,6697,6776,17890,17977,18295,18373,18541,18624,18710,18786,18863,19209,19284,19352", "endColumns": "93,83,89,97,85,78,105,86,88,77,80,82,85,75,76,75,74,67,117", "endOffsets": "3726,3810,3972,4141,4227,6771,6877,17972,18061,18368,18449,18619,18705,18781,18858,18934,19279,19347,19465"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\14cf378553861cbecadb791c6e2341a9\\transformed\\core-1.16.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,358,461,562,664,784", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "149,251,353,456,557,659,779,880"}, "to": {"startLines": "30,31,32,33,34,35,36,184", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2908,3007,3109,3211,3314,3415,3517,18939", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "3002,3104,3206,3309,3410,3512,3632,19035"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\493af54b1b0004569d1870cf1ba0af14\\transformed\\media3-session-1.7.1\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,226,293,391,484,577,654,746,841,917,992,1088,1172,1271,1352,1450,1523,1622,1692,1759,1843,1922,2007", "endColumns": "70,99,66,97,92,92,76,91,94,75,74,95,83,98,80,97,72,98,69,66,83,78,84,88", "endOffsets": "121,221,288,386,479,572,649,741,836,912,987,1083,1167,1266,1347,1445,1518,1617,1687,1754,1838,1917,2002,2091"}, "to": {"startLines": "41,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,132,133,134,135,136,137", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3977,4232,4332,4399,4497,4590,4683,4760,4852,4947,5023,5098,5194,5278,5377,5458,5556,5629,13332,13402,13469,13553,13632,13717", "endColumns": "70,99,66,97,92,92,76,91,94,75,74,95,83,98,80,97,72,98,69,66,83,78,84,88", "endOffsets": "4043,4327,4394,4492,4585,4678,4755,4847,4942,5018,5093,5189,5273,5372,5453,5551,5624,5723,13397,13464,13548,13627,13712,13801"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\c3709e4bfd5d4e5c1ed8a624d490cd12\\transformed\\material-release\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "89", "endOffsets": "140"}, "to": {"startLines": "131", "startColumns": "4", "startOffsets": "13242", "endColumns": "89", "endOffsets": "13327"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\c9938815eb16e1bbe389a6512658438a\\transformed\\material3-release\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,193,330,449,583,700,799,915,1057,1178,1320,1405,1511,1605,1706,1835,1964,2075,2204,2331,2461,2641,2763,2883,3005,3136,3231,3326,3459,3606,3703,3808,3918,4045,4177,4284,4385,4462,4565,4665,4771,4862,4952,5055,5135,5220,5321,5425,5518,5623,5710,5816,5915,6023,6141,6221,6321", "endColumns": "137,136,118,133,116,98,115,141,120,141,84,105,93,100,128,128,110,128,126,129,179,121,119,121,130,94,94,132,146,96,104,109,126,131,106,100,76,102,99,105,90,89,102,79,84,100,103,92,104,86,105,98,107,117,79,99,93", "endOffsets": "188,325,444,578,695,794,910,1052,1173,1315,1400,1506,1600,1701,1830,1959,2070,2199,2326,2456,2636,2758,2878,3000,3131,3226,3321,3454,3601,3698,3803,3913,4040,4172,4279,4380,4457,4560,4660,4766,4857,4947,5050,5130,5215,5316,5420,5513,5618,5705,5811,5910,6018,6136,6216,6316,6410"}, "to": {"startLines": "74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6882,7020,7157,7276,7410,7527,7626,7742,7884,8005,8147,8232,8338,8432,8533,8662,8791,8902,9031,9158,9288,9468,9590,9710,9832,9963,10058,10153,10286,10433,10530,10635,10745,10872,11004,11111,11212,11289,11392,11492,11598,11689,11779,11882,11962,12047,12148,12252,12345,12450,12537,12643,12742,12850,12968,13048,13148", "endColumns": "137,136,118,133,116,98,115,141,120,141,84,105,93,100,128,128,110,128,126,129,179,121,119,121,130,94,94,132,146,96,104,109,126,131,106,100,76,102,99,105,90,89,102,79,84,100,103,92,104,86,105,98,107,117,79,99,93", "endOffsets": "7015,7152,7271,7405,7522,7621,7737,7879,8000,8142,8227,8333,8427,8528,8657,8786,8897,9026,9153,9283,9463,9585,9705,9827,9958,10053,10148,10281,10428,10525,10630,10740,10867,10999,11106,11207,11284,11387,11487,11593,11684,11774,11877,11957,12042,12143,12247,12340,12445,12532,12638,12737,12845,12963,13043,13143,13237"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\114a5d5301d7649ab21683cbabeecc89\\transformed\\media3-exoplayer-1.7.1\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,189,256,322,397,464,596,725", "endColumns": "67,65,66,65,74,66,131,128,88", "endOffsets": "118,184,251,317,392,459,591,720,809"}, "to": {"startLines": "61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "5728,5796,5862,5929,5995,6070,6137,6269,6398", "endColumns": "67,65,66,65,74,66,131,128,88", "endOffsets": "5791,5857,5924,5990,6065,6132,6264,6393,6482"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\64a01e76fd68fe16459fb0e17d50396b\\transformed\\appcompat-1.6.1\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,425,514,619,740,823,905,996,1089,1183,1277,1377,1470,1565,1659,1750,1841,1927,2037,2141,2244,2352,2460,2565,2730,2835", "endColumns": "107,105,105,88,104,120,82,81,90,92,93,93,99,92,94,93,90,90,85,109,103,102,107,107,104,164,104,86", "endOffsets": "208,314,420,509,614,735,818,900,991,1084,1178,1272,1372,1465,1560,1654,1745,1836,1922,2032,2136,2239,2347,2455,2560,2725,2830,2917"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,178", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,425,514,619,740,823,905,996,1089,1183,1277,1377,1470,1565,1659,1750,1841,1927,2037,2141,2244,2352,2460,2565,2730,18454", "endColumns": "107,105,105,88,104,120,82,81,90,92,93,93,99,92,94,93,90,90,85,109,103,102,107,107,104,164,104,86", "endOffsets": "208,314,420,509,614,735,818,900,991,1084,1178,1272,1372,1465,1560,1654,1745,1836,1922,2032,2136,2239,2347,2455,2560,2725,2830,18536"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\2fbbcb27eaccaf382f754b7d51fec492\\transformed\\mediarouter-1.7.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,181,280,391,505,619,729,838,936,1029,1203,1399,1595,1795,1987,2186,2382,2498,2594,2705,2805,2903,3009,3108,3211,3325,3410,3493,3576,3680,3781,3874,3980,4085,4172", "endColumns": "125,98,110,113,113,109,108,97,92,173,195,195,199,191,198,195,115,95,110,99,97,105,98,102,113,84,82,82,103,100,92,105,104,86,92", "endOffsets": "176,275,386,500,614,724,833,931,1024,1198,1394,1590,1790,1982,2181,2377,2493,2589,2700,2800,2898,3004,3103,3206,3320,3405,3488,3571,3675,3776,3869,3975,4080,4167,4260"}, "to": {"startLines": "71,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6571,13806,13905,14016,14130,14244,14354,14463,14561,14654,14828,15024,15220,15420,15612,15811,16007,16123,16219,16330,16430,16528,16634,16733,16836,16950,17035,17118,17201,17305,17406,17499,17605,17710,17797", "endColumns": "125,98,110,113,113,109,108,97,92,173,195,195,199,191,198,195,115,95,110,99,97,105,98,102,113,84,82,82,103,100,92,105,104,86,92", "endOffsets": "6692,13900,14011,14125,14239,14349,14458,14556,14649,14823,15019,15215,15415,15607,15806,16002,16118,16214,16325,16425,16523,16629,16728,16831,16945,17030,17113,17196,17300,17401,17494,17600,17705,17792,17885"}}]}]}