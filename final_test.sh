#!/bin/bash

echo "🎵 音乐播放功能最终测试"
echo "=========================="

# 安装应用
echo "📱 安装应用..."
adb install -r app/build/outputs/apk/debug/app-debug.apk

if [ $? -ne 0 ]; then
    echo "❌ 应用安装失败"
    exit 1
fi

echo "✅ 应用安装成功"

# 清除日志
echo "🧹 清除旧日志..."
adb logcat -c

# 启动应用
echo "🚀 启动应用..."
adb shell am start -n com.craftworks.music/.MainActivity

# 等待应用启动
echo "⏳ 等待应用启动（10秒）..."
sleep 10

# 检查服务启动
echo "🔍 检查服务启动状态..."
SERVICE_LOGS=$(adb logcat -d | grep -E "MUSIC_SERVICE.*SERVICE CREATED")
if [ -z "$SERVICE_LOGS" ]; then
    echo "❌ 服务未启动"
    echo "📋 完整日志："
    adb logcat -d | grep -E "(MUSIC_SERVICE|STARTUP|ERROR)"
    exit 1
else
    echo "✅ 服务启动成功"
fi

# 检查MediaController连接
echo "🔗 检查MediaController连接..."
CONTROLLER_LOGS=$(adb logcat -d | grep -E "MediaController.*connected.*true")
if [ -z "$CONTROLLER_LOGS" ]; then
    echo "⚠️  MediaController可能未连接"
    echo "📋 MediaController日志："
    adb logcat -d | grep -E "MEDIA_CONTROLLER"
else
    echo "✅ MediaController连接成功"
fi

# 开始监控播放日志
echo "🎧 开始监控播放日志..."
echo "请在应用中点击一首歌曲，然后按Ctrl+C停止监控"

# 实时监控播放相关日志
adb logcat | grep -E "(SONG_HELPER|SONGS_SCREEN|PLAYBACK|Player.*isPlaying)" --line-buffered | while read line; do
    echo "🎵 $line"
    
    # 检查是否播放成功
    if echo "$line" | grep -q "Player isPlaying: true"; then
        echo "🎉 播放成功！"
        break
    fi
    
    # 检查是否有错误
    if echo "$line" | grep -q "ERROR\|Error\|error"; then
        echo "❌ 发现错误：$line"
    fi
done
