package com.craftworks.music.managers

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import com.craftworks.music.managers.NavidromeManager.getAllServers
import com.craftworks.music.providers.local.LocalProvider
import com.craftworks.music.showNoProviderDialog
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json

object LocalProviderManager {
    private val folders = mutableListOf<String>()

    fun addFolder(folder: String) {
        Log.d("LOCAL_PROVIDER", "Adding folder: $folder")
        if (!folders.contains(folder)) {
            folders.add(folder)
            if (::sharedPreferences.isInitialized) {
                saveFolders()
                markFirstLaunchCompleted()
                Log.d("LOCAL_PROVIDER", "Folder added successfully: $folder")
            } else {
                Log.e("LOCAL_PROVIDER", "SharedPreferences not initialized, cannot save folder")
            }
        } else {
            Log.w("LOCAL_PROVIDER", "Folder already exists: $folder")
        }
    }

    fun removeFolder(folder: String) {
        Log.d("LOCAL_PROVIDER", "Removing folder: $folder")
        if (folders.remove(folder)) {
            if (::sharedPreferences.isInitialized) {
                saveFolders()
                Log.d("LOCAL_PROVIDER", "Folder removed successfully: $folder")
            } else {
                Log.e("LOCAL_PROVIDER", "SharedPreferences not initialized, cannot save changes")
            }
        } else {
            Log.w("LOCAL_PROVIDER", "Folder not found for removal: $folder")
        }
    }

    fun checkActiveFolders(): Boolean {
        return folders.isNotEmpty()
    }

    fun getAllFolders(): List<String> = folders

    // Save and load local folders.
    private lateinit var sharedPreferences: SharedPreferences
    private val json = Json { ignoreUnknownKeys = true }
    private const val PREF_FOLDERS = "local_folders"
    private const val PREF_FIRST_LAUNCH = "first_launch_completed"

    fun init(context: Context) {
        sharedPreferences = context.getSharedPreferences("LocalProviderPrefs", Context.MODE_PRIVATE)
        loadFolders()

        LocalProvider.getInstance().init(context)

        // Check if this is the first launch across all providers using shared preferences
        val globalPrefs = context.getSharedPreferences("AppGlobalPrefs", Context.MODE_PRIVATE)
        val isFirstLaunch = !globalPrefs.getBoolean("first_launch_completed", false)
        if (isFirstLaunch && getAllServers().isEmpty() && getAllFolders().isEmpty()) {
            showNoProviderDialog.value = true
        }
    }

    fun markFirstLaunchCompleted() {
        if (::sharedPreferences.isInitialized) {
            sharedPreferences.edit().putBoolean(PREF_FIRST_LAUNCH, true).apply()
        }
    }

    private fun markGlobalFirstLaunchCompleted(context: Context) {
        val globalPrefs = context.getSharedPreferences("AppGlobalPrefs", Context.MODE_PRIVATE)
        globalPrefs.edit().putBoolean("first_launch_completed", true).apply()
    }

    private fun saveFolders() {
        val serversJson = json.encodeToString(folders as List<String>)
        sharedPreferences.edit().putString(PREF_FOLDERS, serversJson).apply()
    }

    private fun loadFolders() {
        val foldersJson = sharedPreferences.getString(PREF_FOLDERS, null)
        if (foldersJson != null) {
            val loadedServers: List<String> = json.decodeFromString(foldersJson)
            folders.addAll(loadedServers)
        }
    }
}