{"logs": [{"outputFile": "com.craftworks.music.app-mergeDebugResources-3:/values-in/values-in.xml", "map": [{"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\64a01e76fd68fe16459fb0e17d50396b\\transformed\\appcompat-1.6.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,324,429,516,620,736,819,897,988,1081,1176,1270,1370,1463,1558,1652,1743,1834,1920,2023,2128,2229,2333,2442,2550,2710,2809", "endColumns": "114,103,104,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "215,319,424,511,615,731,814,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1915,2018,2123,2224,2328,2437,2545,2705,2804,2889"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,178", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,324,429,516,620,736,819,897,988,1081,1176,1270,1370,1463,1558,1652,1743,1834,1920,2023,2128,2229,2333,2442,2550,2710,18174", "endColumns": "114,103,104,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "215,319,424,511,615,731,814,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1915,2018,2123,2224,2328,2437,2545,2705,2804,18254"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\886fef481c8ce4a2616ccf22a8cd1328\\transformed\\foundation-release\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,134,220", "endColumns": "78,85,89", "endOffsets": "129,215,305"}, "to": {"startLines": "29,189,190", "startColumns": "4,4,4", "startOffsets": "2809,19183,19269", "endColumns": "78,85,89", "endOffsets": "2883,19264,19354"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\14cf378553861cbecadb791c6e2341a9\\transformed\\core-1.16.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,446,552,670,785", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "145,247,344,441,547,665,780,881"}, "to": {"startLines": "30,31,32,33,34,35,36,184", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2888,2983,3085,3182,3279,3385,3503,18650", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "2978,3080,3177,3274,3380,3498,3613,18746"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\4f725fd807947c49350c5a696b32f0e9\\transformed\\preference-1.2.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,174,261,341,477,646,731", "endColumns": "68,86,79,135,168,84,78", "endOffsets": "169,256,336,472,641,726,805"}, "to": {"startLines": "39,70,174,175,185,191,192", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3790,6394,17793,17873,18751,19359,19444", "endColumns": "68,86,79,135,168,84,78", "endOffsets": "3854,6476,17868,18004,18915,19439,19518"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\b12f5ec7860c79fe319ea01d980e4aac\\transformed\\ui-release\\res\\values-in\\values-in.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "185,275,357,455,555,641,724,815,902,987,1069,1152,1224,1316,1393,1470,1543,1621,1687", "endColumns": "89,81,97,99,85,82,90,86,84,81,82,71,91,76,76,72,77,65,118", "endOffsets": "270,352,450,550,636,719,810,897,982,1064,1147,1219,1311,1388,1465,1538,1616,1682,1801"}, "to": {"startLines": "37,38,40,42,43,72,73,172,173,176,177,179,180,181,182,183,186,187,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3618,3708,3859,4034,4134,6613,6696,17621,17708,18009,18091,18259,18331,18423,18500,18577,18920,18998,19064", "endColumns": "89,81,97,99,85,82,90,86,84,81,82,71,91,76,76,72,77,65,118", "endOffsets": "3703,3785,3952,4129,4215,6691,6782,17703,17788,18086,18169,18326,18418,18495,18572,18645,18993,19059,19178"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\114a5d5301d7649ab21683cbabeecc89\\transformed\\media3-exoplayer-1.7.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,185,250,313,389,453,553,647", "endColumns": "67,61,64,62,75,63,99,93,68", "endOffsets": "118,180,245,308,384,448,548,642,711"}, "to": {"startLines": "61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "5733,5801,5863,5928,5991,6067,6131,6231,6325", "endColumns": "67,61,64,62,75,63,99,93,68", "endOffsets": "5796,5858,5923,5986,6062,6126,6226,6320,6389"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\493af54b1b0004569d1870cf1ba0af14\\transformed\\media3-session-1.7.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,234,303,423,520,604,685,763,857,947,1011,1120,1214,1309,1383,1475,1553,1645,1712,1779,1852,1926,2016", "endColumns": "76,101,68,119,96,83,80,77,93,89,63,108,93,94,73,91,77,91,66,66,72,73,89,93", "endOffsets": "127,229,298,418,515,599,680,758,852,942,1006,1115,1209,1304,1378,1470,1548,1640,1707,1774,1847,1921,2011,2105"}, "to": {"startLines": "41,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,132,133,134,135,136,137", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3957,4220,4322,4391,4511,4608,4692,4773,4851,4945,5035,5099,5208,5302,5397,5471,5563,5641,13168,13235,13302,13375,13449,13539", "endColumns": "76,101,68,119,96,83,80,77,93,89,63,108,93,94,73,91,77,91,66,66,72,73,89,93", "endOffsets": "4029,4317,4386,4506,4603,4687,4768,4846,4940,5030,5094,5203,5297,5392,5466,5558,5636,5728,13230,13297,13370,13444,13534,13628"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\c3709e4bfd5d4e5c1ed8a624d490cd12\\transformed\\material-release\\res\\values-in\\values-in.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "95", "endOffsets": "146"}, "to": {"startLines": "131", "startColumns": "4", "startOffsets": "13072", "endColumns": "95", "endOffsets": "13163"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\c9938815eb16e1bbe389a6512658438a\\transformed\\material3-release\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,289,401,517,615,721,844,991,1114,1264,1351,1455,1548,1652,1770,1890,1999,2139,2277,2406,2584,2706,2826,2949,3072,3166,3267,3387,3520,3622,3729,3836,3978,4125,4234,4334,4410,4506,4601,4719,4808,4893,4992,5072,5155,5254,5353,5450,5550,5637,5740,5839,5943,6060,6140,6245", "endColumns": "118,114,111,115,97,105,122,146,122,149,86,103,92,103,117,119,108,139,137,128,177,121,119,122,122,93,100,119,132,101,106,106,141,146,108,99,75,95,94,117,88,84,98,79,82,98,98,96,99,86,102,98,103,116,79,104,94", "endOffsets": "169,284,396,512,610,716,839,986,1109,1259,1346,1450,1543,1647,1765,1885,1994,2134,2272,2401,2579,2701,2821,2944,3067,3161,3262,3382,3515,3617,3724,3831,3973,4120,4229,4329,4405,4501,4596,4714,4803,4888,4987,5067,5150,5249,5348,5445,5545,5632,5735,5834,5938,6055,6135,6240,6335"}, "to": {"startLines": "74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6787,6906,7021,7133,7249,7347,7453,7576,7723,7846,7996,8083,8187,8280,8384,8502,8622,8731,8871,9009,9138,9316,9438,9558,9681,9804,9898,9999,10119,10252,10354,10461,10568,10710,10857,10966,11066,11142,11238,11333,11451,11540,11625,11724,11804,11887,11986,12085,12182,12282,12369,12472,12571,12675,12792,12872,12977", "endColumns": "118,114,111,115,97,105,122,146,122,149,86,103,92,103,117,119,108,139,137,128,177,121,119,122,122,93,100,119,132,101,106,106,141,146,108,99,75,95,94,117,88,84,98,79,82,98,98,96,99,86,102,98,103,116,79,104,94", "endOffsets": "6901,7016,7128,7244,7342,7448,7571,7718,7841,7991,8078,8182,8275,8379,8497,8617,8726,8866,9004,9133,9311,9433,9553,9676,9799,9893,9994,10114,10247,10349,10456,10563,10705,10852,10961,11061,11137,11233,11328,11446,11535,11620,11719,11799,11882,11981,12080,12177,12277,12364,12467,12566,12670,12787,12867,12972,13067"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\2fbbcb27eaccaf382f754b7d51fec492\\transformed\\mediarouter-1.7.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,187,288,395,507,616,732,842,939,1030,1216,1393,1573,1754,1927,2112,2296,2414,2509,2617,2713,2808,2908,3001,3119,3238,3320,3403,3489,3592,3694,3786,3891,3993,4079", "endColumns": "131,100,106,111,108,115,109,96,90,185,176,179,180,172,184,183,117,94,107,95,94,99,92,117,118,81,82,85,102,101,91,104,101,85,95", "endOffsets": "182,283,390,502,611,727,837,934,1025,1211,1388,1568,1749,1922,2107,2291,2409,2504,2612,2708,2803,2903,2996,3114,3233,3315,3398,3484,3587,3689,3781,3886,3988,4074,4170"}, "to": {"startLines": "71,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6481,13633,13734,13841,13953,14062,14178,14288,14385,14476,14662,14839,15019,15200,15373,15558,15742,15860,15955,16063,16159,16254,16354,16447,16565,16684,16766,16849,16935,17038,17140,17232,17337,17439,17525", "endColumns": "131,100,106,111,108,115,109,96,90,185,176,179,180,172,184,183,117,94,107,95,94,99,92,117,118,81,82,85,102,101,91,104,101,85,95", "endOffsets": "6608,13729,13836,13948,14057,14173,14283,14380,14471,14657,14834,15014,15195,15368,15553,15737,15855,15950,16058,16154,16249,16349,16442,16560,16679,16761,16844,16930,17033,17135,17227,17332,17434,17520,17616"}}]}]}