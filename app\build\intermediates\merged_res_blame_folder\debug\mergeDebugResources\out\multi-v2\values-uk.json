{"logs": [{"outputFile": "com.craftworks.music.app-mergeDebugResources-3:/values-uk/values-uk.xml", "map": [{"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\886fef481c8ce4a2616ccf22a8cd1328\\transformed\\foundation-release\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,136,233", "endColumns": "80,96,99", "endOffsets": "131,228,328"}, "to": {"startLines": "29,189,190", "startColumns": "4,4,4", "startOffsets": "2829,19417,19514", "endColumns": "80,96,99", "endOffsets": "2905,19509,19609"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\14cf378553861cbecadb791c6e2341a9\\transformed\\core-1.16.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,358,459,564,669,782", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "150,252,353,454,559,664,777,878"}, "to": {"startLines": "30,31,32,33,34,35,36,184", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2910,3010,3112,3213,3314,3419,3524,18874", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "3005,3107,3208,3309,3414,3519,3632,18970"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\64a01e76fd68fe16459fb0e17d50396b\\transformed\\appcompat-1.6.1\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,316,424,510,615,733,814,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1930,2036,2142,2240,2347,2454,2559,2729,2829", "endColumns": "108,101,107,85,104,117,80,78,90,92,94,93,99,92,94,94,90,90,98,105,105,97,106,106,104,169,99,81", "endOffsets": "209,311,419,505,610,728,809,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1925,2031,2137,2235,2342,2449,2554,2724,2824,2906"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,178", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,316,424,510,615,733,814,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1930,2036,2142,2240,2347,2454,2559,2729,18401", "endColumns": "108,101,107,85,104,117,80,78,90,92,94,93,99,92,94,94,90,90,98,105,105,97,106,106,104,169,99,81", "endOffsets": "209,311,419,505,610,728,809,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1925,2031,2137,2235,2342,2449,2554,2724,2824,18478"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\b12f5ec7860c79fe319ea01d980e4aac\\transformed\\ui-release\\res\\values-uk\\values-uk.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "187,280,364,466,567,651,733,822,910,992,1077,1165,1237,1326,1402,1479,1556,1636,1706", "endColumns": "92,83,101,100,83,81,88,87,81,84,87,71,88,75,76,76,79,69,122", "endOffsets": "275,359,461,562,646,728,817,905,987,1072,1160,1232,1321,1397,1474,1551,1631,1701,1824"}, "to": {"startLines": "37,38,40,42,43,72,73,172,173,176,177,179,180,181,182,183,186,187,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3637,3730,3888,4072,4173,6710,6792,17835,17923,18228,18313,18483,18555,18644,18720,18797,19144,19224,19294", "endColumns": "92,83,101,100,83,81,88,87,81,84,87,71,88,75,76,76,79,69,122", "endOffsets": "3725,3809,3985,4168,4252,6787,6876,17918,18000,18308,18396,18550,18639,18715,18792,18869,19219,19289,19412"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\2fbbcb27eaccaf382f754b7d51fec492\\transformed\\mediarouter-1.7.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,188,288,394,502,611,724,830,925,1016,1191,1381,1570,1760,1948,2140,2330,2449,2550,2655,2753,2849,2943,3039,3140,3253,3342,3430,3516,3621,3725,3829,3930,4047,4134", "endColumns": "132,99,105,107,108,112,105,94,90,174,189,188,189,187,191,189,118,100,104,97,95,93,95,100,112,88,87,85,104,103,103,100,116,86,94", "endOffsets": "183,283,389,497,606,719,825,920,1011,1186,1376,1565,1755,1943,2135,2325,2444,2545,2650,2748,2844,2938,3034,3135,3248,3337,3425,3511,3616,3720,3824,3925,4042,4129,4224"}, "to": {"startLines": "71,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6577,13794,13894,14000,14108,14217,14330,14436,14531,14622,14797,14987,15176,15366,15554,15746,15936,16055,16156,16261,16359,16455,16549,16645,16746,16859,16948,17036,17122,17227,17331,17435,17536,17653,17740", "endColumns": "132,99,105,107,108,112,105,94,90,174,189,188,189,187,191,189,118,100,104,97,95,93,95,100,112,88,87,85,104,103,103,100,116,86,94", "endOffsets": "6705,13889,13995,14103,14212,14325,14431,14526,14617,14792,14982,15171,15361,15549,15741,15931,16050,16151,16256,16354,16450,16544,16640,16741,16854,16943,17031,17117,17222,17326,17430,17531,17648,17735,17830"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\4f725fd807947c49350c5a696b32f0e9\\transformed\\preference-1.2.1\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,179,267,348,490,659,744", "endColumns": "73,87,80,141,168,84,82", "endOffsets": "174,262,343,485,654,739,822"}, "to": {"startLines": "39,70,174,175,185,191,192", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3814,6489,18005,18086,18975,19614,19699", "endColumns": "73,87,80,141,168,84,82", "endOffsets": "3883,6572,18081,18223,19139,19694,19777"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\c9938815eb16e1bbe389a6512658438a\\transformed\\material3-release\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,289,407,525,624,719,831,969,1085,1232,1316,1416,1509,1605,1721,1845,1950,2091,2228,2363,2552,2679,2803,2932,3053,3147,3248,3374,3504,3602,3707,3816,3961,4112,4220,4320,4395,4490,4586,4705,4791,4878,4977,5057,5143,5242,5346,5441,5541,5630,5737,5833,5936,6054,6134,6249", "endColumns": "117,115,117,117,98,94,111,137,115,146,83,99,92,95,115,123,104,140,136,134,188,126,123,128,120,93,100,125,129,97,104,108,144,150,107,99,74,94,95,118,85,86,98,79,85,98,103,94,99,88,106,95,102,117,79,114,105", "endOffsets": "168,284,402,520,619,714,826,964,1080,1227,1311,1411,1504,1600,1716,1840,1945,2086,2223,2358,2547,2674,2798,2927,3048,3142,3243,3369,3499,3597,3702,3811,3956,4107,4215,4315,4390,4485,4581,4700,4786,4873,4972,5052,5138,5237,5341,5436,5536,5625,5732,5828,5931,6049,6129,6244,6350"}, "to": {"startLines": "74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6881,6999,7115,7233,7351,7450,7545,7657,7795,7911,8058,8142,8242,8335,8431,8547,8671,8776,8917,9054,9189,9378,9505,9629,9758,9879,9973,10074,10200,10330,10428,10533,10642,10787,10938,11046,11146,11221,11316,11412,11531,11617,11704,11803,11883,11969,12068,12172,12267,12367,12456,12563,12659,12762,12880,12960,13075", "endColumns": "117,115,117,117,98,94,111,137,115,146,83,99,92,95,115,123,104,140,136,134,188,126,123,128,120,93,100,125,129,97,104,108,144,150,107,99,74,94,95,118,85,86,98,79,85,98,103,94,99,88,106,95,102,117,79,114,105", "endOffsets": "6994,7110,7228,7346,7445,7540,7652,7790,7906,8053,8137,8237,8330,8426,8542,8666,8771,8912,9049,9184,9373,9500,9624,9753,9874,9968,10069,10195,10325,10423,10528,10637,10782,10933,11041,11141,11216,11311,11407,11526,11612,11699,11798,11878,11964,12063,12167,12262,12362,12451,12558,12654,12757,12875,12955,13070,13176"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\c3709e4bfd5d4e5c1ed8a624d490cd12\\transformed\\material-release\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "92", "endOffsets": "143"}, "to": {"startLines": "131", "startColumns": "4", "startOffsets": "13181", "endColumns": "92", "endOffsets": "13269"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\114a5d5301d7649ab21683cbabeecc89\\transformed\\media3-exoplayer-1.7.1\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,194,262,333,413,486,579,668", "endColumns": "73,64,67,70,79,72,92,88,74", "endOffsets": "124,189,257,328,408,481,574,663,738"}, "to": {"startLines": "61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "5801,5875,5940,6008,6079,6159,6232,6325,6414", "endColumns": "73,64,67,70,79,72,92,88,74", "endOffsets": "5870,5935,6003,6074,6154,6227,6320,6409,6484"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\493af54b1b0004569d1870cf1ba0af14\\transformed\\media3-session-1.7.1\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,137,244,320,441,537,631,703,797,894,974,1040,1147,1231,1325,1403,1503,1582,1681,1755,1827,1910,1997,2096", "endColumns": "81,106,75,120,95,93,71,93,96,79,65,106,83,93,77,99,78,98,73,71,82,86,98,104", "endOffsets": "132,239,315,436,532,626,698,792,889,969,1035,1142,1226,1320,1398,1498,1577,1676,1750,1822,1905,1992,2091,2196"}, "to": {"startLines": "41,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,132,133,134,135,136,137", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3990,4257,4364,4440,4561,4657,4751,4823,4917,5014,5094,5160,5267,5351,5445,5523,5623,5702,13274,13348,13420,13503,13590,13689", "endColumns": "81,106,75,120,95,93,71,93,96,79,65,106,83,93,77,99,78,98,73,71,82,86,98,104", "endOffsets": "4067,4359,4435,4556,4652,4746,4818,4912,5009,5089,5155,5262,5346,5440,5518,5618,5697,5796,13343,13415,13498,13585,13684,13789"}}]}]}