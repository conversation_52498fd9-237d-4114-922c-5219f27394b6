package com.craftworks.music.player

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.media.AudioFocusRequest
import android.media.AudioManager
import android.os.Build
import android.os.Bundle
import android.util.Log
import androidx.annotation.OptIn
import androidx.compose.ui.util.fastFilter
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import androidx.media3.session.MediaButtonReceiver
import androidx.core.math.MathUtils.clamp
import androidx.media3.common.AudioAttributes
import androidx.media3.common.C
import androidx.media3.common.MediaItem
import androidx.media3.common.MediaMetadata
import androidx.media3.common.PlaybackException
import androidx.media3.common.Player
import androidx.media3.common.util.UnstableApi
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.exoplayer.SeekParameters
import androidx.media3.session.LibraryResult
import androidx.media3.session.MediaConstants
import androidx.media3.session.MediaLibraryService
import androidx.media3.session.MediaSession
import androidx.media3.session.MediaSession.MediaItemsWithStartPosition
import androidx.media3.session.SessionError
import com.craftworks.music.MainActivity
import com.craftworks.music.R
import com.craftworks.music.data.toMediaItem
import com.craftworks.music.lyrics.LyricsManager
import com.craftworks.music.managers.CacheManager
import com.craftworks.music.managers.LocalProviderManager
import com.craftworks.music.managers.NavidromeManager
import com.craftworks.music.managers.SettingsManager
import com.craftworks.music.widget.MusicWidgetProvider
import com.craftworks.music.widget.MusicWidget2x2Provider
import com.craftworks.music.providers.getAlbum
import com.craftworks.music.providers.getAlbums
import com.craftworks.music.providers.getPlaylistDetails
import com.craftworks.music.providers.getPlaylists
import com.craftworks.music.providers.getRadios
import com.craftworks.music.providers.getSongs
import com.craftworks.music.providers.navidrome.markNavidromeSongAsPlayed
import com.craftworks.music.providers.searchAlbum
import com.craftworks.music.ui.viewmodels.GlobalViewModels
import com.google.common.collect.ImmutableList
import com.google.common.util.concurrent.Futures
import com.google.common.util.concurrent.ListenableFuture
import com.google.common.util.concurrent.SettableFuture
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.async
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import kotlin.math.pow
import java.io.File
import java.util.Timer
import java.util.TimerTask
import androidx.core.net.toUri

/*
    Thanks to Yurowitz on StackOverflow for this! Used it as a template.
    https://stackoverflow.com/questions/76838126/can-i-define-a-medialibraryservice-without-an-app
*/

@UnstableApi
class ChoraMediaLibraryService : MediaLibraryService() {
    //region Vars
    lateinit var player: Player
    var session: MediaLibrarySession? = null
    private var stateSaveTimer: Timer? = null
    private val serviceMainScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)
    private lateinit var audioManager: AudioManager
    private var audioFocusRequest: AudioFocusRequest? = null
    private var hasAudioFocus = false
    private val serviceIOScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)

    companion object {
        private var instance: ChoraMediaLibraryService? = null
        private const val NOTIFICATION_CHANNEL_ID = "music_playback_channel"
        private const val NOTIFICATION_ID = 1001

        fun getInstance(): ChoraMediaLibraryService? {
            return instance
        }
    }

    private val rootItem = MediaItem.Builder()
        .setMediaId("nodeROOT")
        .setMediaMetadata(
            MediaMetadata.Builder()
                .setIsBrowsable(false)
                .setIsPlayable(false)
                .setMediaType(MediaMetadata.MEDIA_TYPE_FOLDER_MIXED)
                .build()
        )
        .build()

    private val homeItem = MediaItem.Builder()
        .setMediaId("nodeHOME")
        .setMediaMetadata(
            MediaMetadata.Builder()
                .setIsBrowsable(true)
                .setIsPlayable(false)
                .setMediaType(MediaMetadata.MEDIA_TYPE_FOLDER_ALBUMS)
                .setTitle("Home")
                .setExtras(Bundle().apply {
                    putInt(
                        MediaConstants.EXTRAS_KEY_CONTENT_STYLE_BROWSABLE,
                        MediaConstants.EXTRAS_VALUE_CONTENT_STYLE_GRID_ITEM
                    )
                })
                .build()
        )
        .build()

    private val radiosItem = MediaItem.Builder()
        .setMediaId("nodeRADIOS")
        .setMediaMetadata(
            MediaMetadata.Builder()
                .setIsBrowsable(true)
                .setIsPlayable(false)
                .setMediaType(MediaMetadata.MEDIA_TYPE_FOLDER_RADIO_STATIONS)
                .setTitle("Radios")
                .build()
        )
        .build()

    private val playlistsItem = MediaItem.Builder()
        .setMediaId("nodePLAYLISTS")
        .setMediaMetadata(
            MediaMetadata.Builder()
                .setIsBrowsable(true)
                .setIsPlayable(false)
                .setMediaType(MediaMetadata.MEDIA_TYPE_FOLDER_PLAYLISTS)
                .setTitle("Playlists")
                .build()
        )
        .build()

    private val rootHierarchy = listOf(homeItem, radiosItem, playlistsItem)



    var aHomeScreenItems = mutableListOf<MediaItem>()
    var aRadioScreenItems = mutableListOf<MediaItem>()
    var aPlaylistScreenItems = mutableListOf<MediaItem>()

    var aFolderSongs = mutableListOf<MediaItem>()

    //endregion

    @OptIn(UnstableApi::class)
    override fun onCreate() {
        super.onCreate()

        instance = this

        Log.d("MUSIC_SERVICE", "=== SERVICE CREATED ===")
        Log.d("MUSIC_SERVICE", "onCreate called")

        // Create notification channel for foreground service
        createNotificationChannel()

        if (session == null) {
            Log.d("MUSIC_SERVICE", "Initializing player and session...")
            initializePlayer()
        } else {
            Log.d("MUSIC_SERVICE", "MediaSession already initialized, not recreating")
        }

        // Start periodic state saving timer
        startStateSaveTimer()

        Log.d("MUSIC_SERVICE", "Service onCreate completed")
    }

    @OptIn(UnstableApi::class)
    fun initializePlayer() {
        Log.d("MUSIC_SERVICE", "Initializing ExoPlayer...")

        try {
            // Initialize audio manager for manual focus handling
            audioManager = getSystemService(Context.AUDIO_SERVICE) as AudioManager
            Log.d("MUSIC_SERVICE", "AudioManager initialized")

            player = ExoPlayer.Builder(this)
                .setSeekParameters(SeekParameters.EXACT)
                .setWakeMode(
                    // Prioritize local playback - only use network mode if actively using Navidrome
                    if (NavidromeManager.checkActiveServers() && NavidromeManager.getAllServers().isNotEmpty())
                        C.WAKE_MODE_NETWORK
                    else
                        C.WAKE_MODE_LOCAL
                )
                .setHandleAudioBecomingNoisy(false) // Disable to prevent navigation-triggered pauses
                .setAudioAttributes(
                    AudioAttributes.Builder()
                        .setUsage(C.USAGE_MEDIA)
                        .setContentType(C.AUDIO_CONTENT_TYPE_MUSIC)
                        .build(),
                    true // Enable automatic audio focus handling for better background playback
                )
                .build()

            player.repeatMode = Player.REPEAT_MODE_OFF
            player.shuffleModeEnabled = false

            // Initialize player listeners - THIS WAS MISSING!
            initializePlayerListeners()

            Log.d("MUSIC_SERVICE", "ExoPlayer initialized successfully")
            Log.d("MUSIC_SERVICE", "Player state: ${player.playbackState}")
            Log.d("MUSIC_SERVICE", "Player listeners initialized")

        } catch (e: Exception) {
            Log.e("MUSIC_SERVICE", "Failed to initialize ExoPlayer: ${e.message}", e)
            throw e
        }
    }

    private fun requestAudioFocus(): Boolean {
        // Always return true to disable manual audio focus handling
        // Let ExoPlayer handle audio focus automatically for better background playback
        Log.d("AUDIO_FOCUS", "Audio focus request bypassed - using ExoPlayer automatic handling")
        hasAudioFocus = true
        return true
    }

    private fun abandonAudioFocus() {
        if (!hasAudioFocus) return

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            audioFocusRequest?.let { request ->
                audioManager.abandonAudioFocusRequest(request)
            }
        } else {
            @Suppress("DEPRECATION")
            audioManager.abandonAudioFocus { focusChange ->
                handleAudioFocusChange(focusChange)
            }
        }
        hasAudioFocus = false
        Log.d("AUDIO_FOCUS", "Audio focus abandoned")
    }

    private fun handleAudioFocusChange(focusChange: Int) {
        when (focusChange) {
            AudioManager.AUDIOFOCUS_GAIN -> {
                Log.d("AUDIO_FOCUS", "Audio focus gained")
                hasAudioFocus = true
                // Only resume if we were playing before losing focus
                if (::player.isInitialized && !player.isPlaying) {
                    player.play()
                }
            }
            AudioManager.AUDIOFOCUS_LOSS -> {
                Log.d("AUDIO_FOCUS", "Audio focus lost permanently - ignoring for continuous playback")
                hasAudioFocus = false
                // Don't pause on permanent focus loss to allow background playback
                // This prevents pausing when switching apps or locking screen
            }
            AudioManager.AUDIOFOCUS_LOSS_TRANSIENT -> {
                Log.d("AUDIO_FOCUS", "Audio focus lost temporarily - ignoring for background playback")
                // Don't pause for temporary focus loss to allow background playback
                // This prevents pausing when switching apps
            }
            AudioManager.AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK -> {
                Log.d("AUDIO_FOCUS", "Audio focus lost - can duck")
                // Lower volume instead of pausing for notifications, etc.
                if (::player.isInitialized) {
                    player.volume = 0.3f
                }
            }
        }
    }

    private fun initializePlayerListeners() {
        player.addListener(object : Player.Listener {
            override fun onMediaItemTransition(mediaItem: MediaItem?, reason: Int) {
                super.onMediaItemTransition(mediaItem, reason)

                // Update foreground notification when song changes
                updateForegroundNotification(mediaItem)

                // Update widgets when media item changes
                try {
                    MusicWidgetProvider.updateWidget(
                        this@ChoraMediaLibraryService,
                        player.isPlaying,
                        mediaItem?.mediaMetadata
                    )
                    MusicWidget2x2Provider.updateWidget(
                        this@ChoraMediaLibraryService,
                        player.isPlaying,
                        mediaItem?.mediaMetadata
                    )
                } catch (e: Exception) {
                    Log.e("WIDGET_UPDATE", "Error updating widget on media transition: ${e.message}")
                }

                // Apply ReplayGain
                if (mediaItem?.mediaMetadata?.extras?.getFloat("replayGain") != null) {
                    player.volume = clamp(
                        (10f.pow(
                            ((mediaItem.mediaMetadata.extras?.getFloat("replayGain") ?: 0f) / 20f)
                        )), 0f, 1f
                    )
                    Log.d("REPLAY GAIN", "Setting ReplayGain to ${player.volume}")
                }

                serviceMainScope.launch {
                    if (NavidromeManager.checkActiveServers() &&
                        mediaItem?.mediaMetadata?.extras?.getString("navidromeID")?.startsWith("Local") == false &&
                        mediaItem.mediaMetadata.mediaType != MediaMetadata.MEDIA_TYPE_RADIO_STATION
                    ) {
                        async {
                            markNavidromeSongAsPlayed(
                                mediaItem.mediaMetadata.extras?.getString("navidromeID") ?: "",
                                player.currentPosition.toFloat(),
                                player.duration.toFloat()
                            )
                        }
                    }
                }

                serviceIOScope.launch { async { LyricsManager.getLyrics(mediaItem?.mediaMetadata) } }

                // Handle caching for non-local, non-radio songs - only if Navidrome is active
                if (mediaItem != null &&
                    mediaItem.mediaMetadata.mediaType == MediaMetadata.MEDIA_TYPE_MUSIC &&
                    mediaItem.mediaMetadata.extras?.getString("navidromeID")?.startsWith("Local") == false &&
                    NavidromeManager.checkActiveServers()) {

                    serviceIOScope.launch {
                        async {
                            try {
                                val mediaUrl = mediaItem.mediaId
                                if (mediaUrl.isNotEmpty()) {
                                    Log.d("CACHE", "Caching Navidrome song: ${mediaItem.mediaMetadata.title}")
                                    CacheManager.cacheSong(this@ChoraMediaLibraryService, mediaItem.mediaMetadata, mediaUrl)
                                }
                            } catch (e: Exception) {
                                Log.e("CACHE", "Error caching song: ${e.message}")
                            }
                        }
                    }
                } else if (mediaItem?.mediaMetadata?.extras?.getString("navidromeID")?.startsWith("Local") == true) {
                    Log.d("PLAYBACK", "Playing local song: ${mediaItem.mediaMetadata.title} - no caching needed")
                }
            }

            override fun onIsPlayingChanged(isPlaying: Boolean) {
                super.onIsPlayingChanged(isPlaying)
                Log.d("MUSIC_SERVICE", "=== PLAY STATE CHANGED ===")
                Log.d("MUSIC_SERVICE", "Play state changed: $isPlaying")
                Log.d("MUSIC_SERVICE", "Current media item: ${player.currentMediaItem?.mediaMetadata?.title}")

                // Start/stop foreground service based on playback state
                if (isPlaying) {
                    Log.d("MUSIC_SERVICE", "Starting foreground service...")
                    // Let ExoPlayer handle audio focus automatically
                    startForegroundService()
                    Log.d("MUSIC_SERVICE", "Foreground service start requested")
                } else {
                    Log.d("MUSIC_SERVICE", "Stopping foreground service...")
                    stopForeground(STOP_FOREGROUND_DETACH)
                }

                // Update widgets when play state changes
                try {
                    Log.d("MUSIC_SERVICE", "Updating widgets - isPlaying: $isPlaying")
                    MusicWidgetProvider.updateWidget(
                        this@ChoraMediaLibraryService,
                        isPlaying,
                        player.currentMediaItem?.mediaMetadata
                    )
                    MusicWidget2x2Provider.updateWidget(
                        this@ChoraMediaLibraryService,
                        isPlaying,
                        player.currentMediaItem?.mediaMetadata
                    )
                    Log.d("MUSIC_SERVICE", "Widgets updated successfully")
                } catch (e: Exception) {
                    Log.e("MUSIC_SERVICE", "Error updating widget on play state change: ${e.message}", e)
                }
            }

            override fun onPlayerError(error: PlaybackException) {
                error.printStackTrace()
                Log.e("PLAYER", error.stackTraceToString())
            }
        })

        try {
            Log.d("MUSIC_SERVICE", "Creating MediaLibrarySession...")
            session = MediaLibrarySession.Builder(this, player, LibrarySessionCallback())
                .setId("AutoSession")
                .build()
            Log.d("MUSIC_SERVICE", "MediaLibrarySession created successfully")
        } catch (e: Exception) {
            Log.e("MUSIC_SERVICE", "Failed to create MediaLibrarySession: ${e.message}", e)
            throw e
        }

        Log.d("MUSIC_SERVICE", "MediaLibraryService initialization completed")

        // Manually restore playback state on service initialization
        restorePlaybackState()
    }

    override fun onGetSession(controllerInfo: MediaSession.ControllerInfo): MediaLibrarySession? {
        return session
    }

    private inner class LibrarySessionCallback : MediaLibrarySession.Callback {
        override fun onPostConnect(session: MediaSession, controller: MediaSession.ControllerInfo) {
            // Initialize providers asynchronously to avoid blocking UI
            serviceIOScope.launch {
                Log.d("INIT", "Starting non-blocking provider initialization...")

                try {
                    // Prioritize local provider initialization for better performance
                    Log.d("INIT", "Initializing Local Provider first...")
                    LocalProviderManager.init(this@ChoraMediaLibraryService)

                    // Initialize Navidrome in background if configured
                    if (NavidromeManager.getAllServers().isNotEmpty()) {
                        Log.d("INIT", "Initializing Navidrome Manager in background...")
                        async {
                            try {
                                NavidromeManager.init(this@ChoraMediaLibraryService)
                            } catch (e: Exception) {
                                Log.e("INIT", "Navidrome initialization failed: ${e.message}", e)
                            }
                        }
                    } else {
                        Log.d("INIT", "No Navidrome servers configured, skipping initialization")
                    }

                    // Load home screen items in background
                    if (session.isAutoCompanionController(controller)) {
                        async {
                            try {
                                getHomeScreenItems()
                                <EMAIL>?.notifyChildrenChanged(
                                    "nodeHOME",
                                    aHomeScreenItems.size,
                                    null
                                )
                            } catch (e: Exception) {
                                Log.e("INIT", "Failed to load home screen items: ${e.message}", e)
                            }
                        }
                    }

                    // Refresh ViewModels in background
                    async {
                        try {
                            GlobalViewModels.refreshAll()
                        } catch (e: Exception) {
                            Log.e("INIT", "Failed to refresh ViewModels: ${e.message}", e)
                        }
                    }

                    Log.d("INIT", "Provider initialization completed")
                } catch (e: Exception) {
                    Log.e("INIT", "Error during provider initialization: ${e.message}", e)
                }
            }
            super.onPostConnect(session, controller)
        }

        @OptIn(UnstableApi::class)
        override fun onSetMediaItems(
            mediaSession: MediaSession,
            controller: MediaSession.ControllerInfo,
            mediaItems: List<MediaItem>,
            startIndex: Int,
            startPositionMs: Long
        ): ListenableFuture<MediaItemsWithStartPosition> {
            // We need to use URI from requestMetaData because of https://github.com/androidx/media/issues/282
            val updatedStartIndex =
                SongHelper.currentTracklist.indexOfFirst { it.mediaId == mediaItems[0].mediaId }

            val currentTracklist =
                if (updatedStartIndex != -1) {
                    SongHelper.currentTracklist
                } else {
                    SongHelper.currentTracklist = mediaItems.toMutableList()
                    mediaItems
                }

            val updatedMediaItems: List<MediaItem> =
                currentTracklist.map { mediaItem ->
                    // Check for cached files for non-local, non-radio songs
                    val finalUri = if (mediaItem.mediaMetadata.mediaType == MediaMetadata.MEDIA_TYPE_MUSIC &&
                        mediaItem.mediaMetadata.extras?.getString("navidromeID")?.startsWith("Local") == false) {

                        try {
                            val cachedPath = runBlocking {
                                CacheManager.getCachedSongPath(this@ChoraMediaLibraryService, mediaItem.mediaMetadata)
                            }

                            if (cachedPath != null) {
                                Log.d("CACHE", "Using cached file for ${mediaItem.mediaMetadata.title}: $cachedPath")
                                File(cachedPath).toUri().toString()
                            } else {
                                mediaItem.mediaId
                            }
                        } catch (e: Exception) {
                            Log.e("CACHE", "Error checking cache for ${mediaItem.mediaMetadata.title}: ${e.message}")
                            mediaItem.mediaId
                        }
                    } else {
                        mediaItem.mediaId
                    }

                    MediaItem.Builder()
                        .setMediaId(mediaItem.mediaId)
                        .setMediaMetadata(mediaItem.mediaMetadata)
                        .setUri(finalUri)
                        .build()
                }

            return super.onSetMediaItems(
                mediaSession,
                controller,
                updatedMediaItems,
                updatedStartIndex,
                startPositionMs
            )
        }


        override fun onGetLibraryRoot(
            session: MediaLibrarySession,
            browser: MediaSession.ControllerInfo,
            params: LibraryParams?
        ): ListenableFuture<LibraryResult<MediaItem>> {
            return Futures.immediateFuture(LibraryResult.ofItem(rootItem, params))
        }

        @OptIn(UnstableApi::class)
        override fun onGetChildren(
            session: MediaLibrarySession,
            browser: MediaSession.ControllerInfo,
            parentId: String,
            page: Int,
            pageSize: Int,
            params: LibraryParams?
        ): ListenableFuture<LibraryResult<ImmutableList<MediaItem>>> {
            return Futures.immediateFuture(
                try {
                    LibraryResult.ofItemList(
                        when (parentId) {
                            "nodeROOT" -> rootHierarchy
                            "nodeHOME" -> getHomeScreenItems()
                            "nodeRADIOS" -> getRadioItems()
                            "nodePLAYLISTS" -> getPlaylistItems()
                            else -> {
                                val mediaItem =
                                    aHomeScreenItems.find { it.mediaId == parentId }
                                        ?: aPlaylistScreenItems.find { it.mediaId == parentId }
                                getFolderItems(
                                    parentId,
                                    mediaItem?.mediaMetadata?.mediaType
                                        ?: MediaMetadata.MEDIA_TYPE_ALBUM
                                )
                            }
                        }, params)
                } catch (_: Exception) {
                    LibraryResult.ofError(SessionError.ERROR_UNKNOWN)
                }
            )
        }


        @OptIn(UnstableApi::class)
        override fun onGetItem(
            session: MediaLibrarySession,
            browser: MediaSession.ControllerInfo,
            mediaId: String
        ): ListenableFuture<LibraryResult<MediaItem>> {
            val mediaItem = aFolderSongs.find { it.mediaId == mediaId }
                ?: aRadioScreenItems.find { it.mediaId == mediaId }
                ?: return Futures.immediateFuture(LibraryResult.ofError(SessionError.ERROR_BAD_VALUE))

            return Futures.immediateFuture(
                LibraryResult.ofItem(
                    mediaItem,
                    LibraryParams.Builder().build()
                )
            )
        }

        override fun onSubscribe(
            session: MediaLibrarySession,
            browser: MediaSession.ControllerInfo,
            parentId: String,
            params: LibraryParams?
        ): ListenableFuture<LibraryResult<Void>> {
            session.notifyChildrenChanged(
                parentId,
                when (parentId) {
                    "nodeROOT" -> 2
                    "nodeHOME" -> aHomeScreenItems.size
                    "nodeRADIOS" -> aRadioScreenItems.size
                    "nodePLAYLISTS" -> aPlaylistScreenItems.size
                    else -> 0
                },
                params
            )

            return Futures.immediateFuture(LibraryResult.ofVoid())
        }

        override fun onPlaybackResumption(
            mediaSession: MediaSession,
            controller: MediaSession.ControllerInfo
        ): ListenableFuture<MediaItemsWithStartPosition> {
            val settable = SettableFuture.create<MediaItemsWithStartPosition>()
            serviceMainScope.launch {
                Log.d("RESUMPTION", "Getting onPlaybackResumption")
                try {
                    val settingsManager = SettingsManager(applicationContext)
                    settingsManager.playbackResumptionPlaylistWithStartPosition.collect { resumptionData ->
                        Log.d("RESUMPTION", "Got mediaitems: ${resumptionData.mediaItems.size} items, startIndex: ${resumptionData.startIndex}, startPosition: ${resumptionData.startPositionMs}")

                        if (resumptionData.mediaItems.isNotEmpty()) {
                            settable.set(resumptionData)

                            withContext(Dispatchers.Main) {
                                try {
                                    player.setMediaItems(resumptionData.mediaItems)
                                    player.prepare()

                                    // Don't auto-play on resumption, just prepare
                                    player.playWhenReady = false

                                    player.seekTo(resumptionData.startIndex, resumptionData.startPositionMs)

                                    SongHelper.currentTracklist = resumptionData.mediaItems.toMutableList()

                                    Log.d("RESUMPTION", "Successfully restored playback state")
                                } catch (e: Exception) {
                                    Log.e("RESUMPTION", "Error restoring playback state: ${e.message}")
                                }
                            }
                        } else {
                            Log.d("RESUMPTION", "No saved playlist found, returning empty state")
                            settable.set(MediaSession.MediaItemsWithStartPosition(emptyList(), 0, 0L))
                        }
                        return@collect // Exit after first emission
                    }
                } catch (e: Exception) {
                    Log.e("RESUMPTION", "Error in onPlaybackResumption: ${e.message}")
                    settable.set(MediaSession.MediaItemsWithStartPosition(emptyList(), 0, 0L))
                }
            }
            return settable
        }

        override fun onGetSearchResult(
            session: MediaLibrarySession,
            browser: MediaSession.ControllerInfo,
            query: String,
            page: Int,
            pageSize: Int,
            params: LibraryParams?
        ): ListenableFuture<LibraryResult<ImmutableList<MediaItem>>> {
            return Futures.immediateFuture(
                LibraryResult.ofItemList(
                    runBlocking {
                        SongHelper.currentTracklist = getSongs(query).toMutableList()
                        SongHelper.currentTracklist
                    },
                    LibraryParams.Builder().build()
                )
            )
        }

        override fun onSearch(
            session: MediaLibrarySession,
            browser: MediaSession.ControllerInfo,
            query: String,
            params: LibraryParams?
        ): ListenableFuture<LibraryResult<Void>> {
            println("onSearch!!!")

            session.notifySearchResultChanged(
                browser,
                query,
                runBlocking {
                    getSongs(query).size +
                            searchAlbum(query).size +
                            getRadios(this@ChoraMediaLibraryService).fastFilter {
                                it.name.contains(
                                    query
                                )
                            }.size +
                            getPlaylists(baseContext).fastFilter {
                                it.mediaMetadata.title?.contains(
                                    query
                                ) == true
                            }.size
                },
                LibraryParams.Builder().build()
            )

            return Futures.immediateFuture(LibraryResult.ofVoid())
        }
    }

    private fun restorePlaybackState() {
        serviceMainScope.launch {
            try {
                Log.d("RESTORE_STATE", "Attempting to restore playback state")
                val settingsManager = SettingsManager(applicationContext)
                settingsManager.playbackResumptionPlaylistWithStartPosition.collect { resumptionData ->
                    Log.d("RESTORE_STATE", "Got resumption data: ${resumptionData.mediaItems.size} items")

                    if (resumptionData.mediaItems.isNotEmpty()) {
                        withContext(Dispatchers.Main) {
                            try {
                                player.setMediaItems(resumptionData.mediaItems)
                                player.prepare()

                                // Don't auto-play on restoration
                                player.playWhenReady = false

                                player.seekTo(resumptionData.startIndex, resumptionData.startPositionMs)

                                SongHelper.currentTracklist = resumptionData.mediaItems.toMutableList()

                                Log.d("RESTORE_STATE", "Successfully restored: ${resumptionData.mediaItems[resumptionData.startIndex].mediaMetadata.title} at position ${resumptionData.startPositionMs}ms")
                            } catch (e: Exception) {
                                Log.e("RESTORE_STATE", "Error restoring playback state: ${e.message}")
                            }
                        }
                    } else {
                        Log.d("RESTORE_STATE", "No saved state found")
                    }
                    return@collect // Exit after first emission
                }
            } catch (e: Exception) {
                Log.e("RESTORE_STATE", "Error in restorePlaybackState: ${e.message}")
            }
        }
    }

    override fun onDestroy() {
        saveState()
        stopStateSaveTimer()
        abandonAudioFocus()
        session?.release()
        instance = null
        super.onDestroy()
    }

    private fun startStateSaveTimer() {
        try {
            stopStateSaveTimer() // Ensure any existing timer is stopped
            stateSaveTimer = Timer("StateSaveTimer", true) // Use daemon thread
            stateSaveTimer?.scheduleAtFixedRate(object : TimerTask() {
                override fun run() {
                    try {
                        if (::player.isInitialized && player.isPlaying) {
                            saveState()
                        }
                    } catch (e: Exception) {
                        Log.e("STATE_SAVE_TIMER", "Error in state save timer: ${e.message}")
                    }
                }
            }, 30000, 30000) // Save every 30 seconds during playback
        } catch (e: Exception) {
            Log.e("STATE_SAVE_TIMER", "Error starting state save timer: ${e.message}")
        }
    }

    private fun stopStateSaveTimer() {
        try {
            stateSaveTimer?.cancel()
            stateSaveTimer?.purge()
            stateSaveTimer = null
        } catch (e: Exception) {
            Log.e("STATE_SAVE_TIMER", "Error stopping state save timer: ${e.message}")
        }
    }

    fun saveState() {
        try {
            if (!::player.isInitialized) {
                Log.w("STATE_SAVE", "Player not initialized, skipping state save")
                return
            }

            runBlocking {
                try {
                    Log.d(
                        "STATE_SAVE",
                        "Saving state! Playlist: ${SongHelper.currentTracklist.map { it.mediaMetadata.title }}, current index: ${player.currentMediaItemIndex}, current position: ${player.currentPosition}"
                    )

                    SettingsManager(applicationContext).setPlaybackResumption(
                        SongHelper.currentTracklist,
                        player.currentMediaItemIndex,
                        player.currentPosition
                    )
                } catch (e: Exception) {
                    Log.e("STATE_SAVE", "Error saving playback state: ${e.message}")
                }
            }
        } catch (e: Exception) {
            Log.e("STATE_SAVE", "Error in saveState: ${e.message}")
        }
    }

    //region getChildren
    private fun getHomeScreenItems(): MutableList<MediaItem> {
        println("GETTING ANDROID AUTO SCREEN ITEMS")
        runBlocking {
            if (aHomeScreenItems.isEmpty()) {
                val recentlyPlayedAlbums = async { getAlbums("recent", 6) }.await()
                val mostPlayedAlbums = async { getAlbums("frequent", 6) }.await()

                recentlyPlayedAlbums.forEach { album ->
                    aHomeScreenItems.add(
                        album.apply {
                            this.mediaMetadata.extras?.putString(
                                MediaConstants.EXTRAS_KEY_CONTENT_STYLE_GROUP_TITLE,
                                <EMAIL>(R.string.recently_played)
                            )
                        }
                    )
                }

                mostPlayedAlbums.forEach { album ->
                    aHomeScreenItems.add(
                        album.apply {
                            this.mediaMetadata.extras?.putString(
                                MediaConstants.EXTRAS_KEY_CONTENT_STYLE_GROUP_TITLE,
                                <EMAIL>(R.string.most_played)
                            )
                        }
                    )
                }
            }
        }
        return aHomeScreenItems
    }

    private fun getRadioItems(): MutableList<MediaItem> {
        runBlocking {
            if (aRadioScreenItems.isEmpty()) {
                getRadios(baseContext).forEach { radio ->
                    aRadioScreenItems.add(radio.toMediaItem())
                }
            }
            SongHelper.currentTracklist = aRadioScreenItems
        }
        return aRadioScreenItems
    }

    private fun getPlaylistItems(): MutableList<MediaItem> {
        runBlocking {
            if (aPlaylistScreenItems.isEmpty()) {
                getPlaylists(baseContext)
            }
            SongHelper.currentTracklist = aPlaylistScreenItems
        }
        return aPlaylistScreenItems
    }

    private fun getFolderItems(parentId: String, type: Int): MutableList<MediaItem> {
        runBlocking {
            aFolderSongs.clear()
            when (type) {
                MediaMetadata.MEDIA_TYPE_ALBUM -> {
                    val albumSongs = getAlbum(parentId)
                    aFolderSongs.addAll(
                        albumSongs?.subList(1, albumSongs.size) ?: emptyList()
                    )
                }

                MediaMetadata.MEDIA_TYPE_PLAYLIST -> {
                    aFolderSongs.addAll(
                        getPlaylistDetails(parentId) ?: emptyList()
                    )
                }

                else -> aFolderSongs.clear()
            }
            SongHelper.currentTracklist = aFolderSongs
        }

        return aFolderSongs
    }
    //endregion

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                NOTIFICATION_CHANNEL_ID,
                "Music Playback",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Shows currently playing music"
                setShowBadge(false)
            }

            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(channel)
        }
    }

    private fun startForegroundService() {
        try {
            val notification = createNotification(player.currentMediaItem)
            Log.d("MUSIC_SERVICE", "Starting foreground service with notification")
            startForeground(NOTIFICATION_ID, notification)
            Log.d("MUSIC_SERVICE", "Foreground service started successfully")
        } catch (e: Exception) {
            Log.e("MUSIC_SERVICE", "Failed to start foreground service: ${e.message}", e)
        }
    }

    private fun updateForegroundNotification(mediaItem: MediaItem?) {
        if (player.isPlaying) {
            val notification = createNotification(mediaItem)
            val notificationManager = NotificationManagerCompat.from(this)
            notificationManager.notify(NOTIFICATION_ID, notification)
        }
    }

    private fun createNotification(mediaItem: MediaItem?): Notification {
        Log.d("MUSIC_SERVICE", "Creating notification for: ${mediaItem?.mediaMetadata?.title}")

        return NotificationCompat.Builder(this, NOTIFICATION_CHANNEL_ID)
            .setContentTitle(mediaItem?.mediaMetadata?.title ?: "Unknown Title")
            .setContentText(mediaItem?.mediaMetadata?.artist ?: "Unknown Artist")
            .setSmallIcon(R.drawable.ic_music_note)
            .setLargeIcon(null as android.graphics.Bitmap?) // TODO: Add album art
            .setContentIntent(
                PendingIntent.getActivity(
                    this,
                    0,
                    Intent(this, MainActivity::class.java),
                    PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
                )
            )
            .setDeleteIntent(
                PendingIntent.getBroadcast(
                    this,
                    0,
                    Intent(this, MediaButtonReceiver::class.java),
                    PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
                )
            )
            .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
            .setOnlyAlertOnce(true)
            .setOngoing(player.isPlaying)
            .setCategory(NotificationCompat.CATEGORY_TRANSPORT)
            .setStyle(
                androidx.media.app.NotificationCompat.MediaStyle()
                    .setShowActionsInCompactView(0, 1, 2)
                    .setShowCancelButton(true)
                    .setCancelButtonIntent(
                        PendingIntent.getBroadcast(
                            this,
                            0,
                            Intent(this, MediaButtonReceiver::class.java),
                            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
                        )
                    )
            )
            .addAction(createNotificationAction("Previous", "android.intent.action.MEDIA_PREVIOUS", R.drawable.media3_notification_seek_to_previous))
            .addAction(createNotificationAction(
                if (player.isPlaying) "Pause" else "Play",
                if (player.isPlaying) "android.intent.action.MEDIA_PAUSE" else "android.intent.action.MEDIA_PLAY",
                if (player.isPlaying) R.drawable.media3_notification_pause else android.R.drawable.ic_media_play
            ))
            .addAction(createNotificationAction("Next", "android.intent.action.MEDIA_NEXT", R.drawable.media3_notification_seek_to_next))
            .build()
    }

    private fun createNotificationAction(title: String, action: String, icon: Int): NotificationCompat.Action {
        val intent = Intent(action).apply {
            setPackage(packageName)
        }
        val pendingIntent = PendingIntent.getBroadcast(
            this,
            action.hashCode(),
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        return NotificationCompat.Action.Builder(icon, title, pendingIntent).build()
    }


}