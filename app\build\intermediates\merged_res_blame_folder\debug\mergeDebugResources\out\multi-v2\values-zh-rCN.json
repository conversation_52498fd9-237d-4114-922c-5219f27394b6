{"logs": [{"outputFile": "com.craftworks.music.app-mergeDebugResources-3:/values-zh-rCN/values-zh-rCN.xml", "map": [{"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\114a5d5301d7649ab21683cbabeecc89\\transformed\\media3-exoplayer-1.7.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,111,167,225,278,350,404,479,557", "endColumns": "55,55,57,52,71,53,74,77,58", "endOffsets": "106,162,220,273,345,399,474,552,611"}, "to": {"startLines": "61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "5113,5169,5225,5283,5336,5408,5462,5537,5615", "endColumns": "55,55,57,52,71,53,74,77,58", "endOffsets": "5164,5220,5278,5331,5403,5457,5532,5610,5669"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\493af54b1b0004569d1870cf1ba0af14\\transformed\\media3-session-1.7.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,122,198,257,334,410,476,546,610,679,745,798,877,944,1022,1088,1166,1228,1299,1364,1428,1497,1569,1645", "endColumns": "66,75,58,76,75,65,69,63,68,65,52,78,66,77,65,77,61,70,64,63,68,71,75,79", "endOffsets": "117,193,252,329,405,471,541,605,674,740,793,872,939,1017,1083,1161,1223,1294,1359,1423,1492,1564,1640,1720"}, "to": {"startLines": "41,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,132,133,134,135,136,137", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3701,3936,4012,4071,4148,4224,4290,4360,4424,4493,4559,4612,4691,4758,4836,4902,4980,5042,11634,11699,11763,11832,11904,11980", "endColumns": "66,75,58,76,75,65,69,63,68,65,52,78,66,77,65,77,61,70,64,63,68,71,75,79", "endOffsets": "3763,4007,4066,4143,4219,4285,4355,4419,4488,4554,4607,4686,4753,4831,4897,4975,5037,5108,11694,11758,11827,11899,11975,12055"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\c3709e4bfd5d4e5c1ed8a624d490cd12\\transformed\\material-release\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "84", "endOffsets": "135"}, "to": {"startLines": "131", "startColumns": "4", "startOffsets": "11549", "endColumns": "84", "endOffsets": "11629"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\4f725fd807947c49350c5a696b32f0e9\\transformed\\preference-1.2.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,171,252,322,442,610,690", "endColumns": "65,80,69,119,167,79,76", "endOffsets": "166,247,317,437,605,685,762"}, "to": {"startLines": "39,70,174,175,185,191,192", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3550,5674,15568,15638,16451,17032,17112", "endColumns": "65,80,69,119,167,79,76", "endOffsets": "3611,5750,15633,15753,16614,17107,17184"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\64a01e76fd68fe16459fb0e17d50396b\\transformed\\appcompat-1.6.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,295,395,477,574,680,757,832,923,1016,1113,1209,1303,1396,1491,1583,1674,1765,1843,1939,2034,2129,2226,2322,2420,2568,2662", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "195,290,390,472,569,675,752,827,918,1011,1108,1204,1298,1391,1486,1578,1669,1760,1838,1934,2029,2124,2221,2317,2415,2563,2657,2736"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,178", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,295,395,477,574,680,757,832,923,1016,1113,1209,1303,1396,1491,1583,1674,1765,1843,1939,2034,2129,2226,2322,2420,2568,15906", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "195,290,390,472,569,675,752,827,918,1011,1108,1204,1298,1391,1486,1578,1669,1760,1838,1934,2029,2124,2221,2317,2415,2563,2657,15980"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\b12f5ec7860c79fe319ea01d980e4aac\\transformed\\ui-release\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "177,254,330,415,506,583,657,734,812,887,960,1035,1103,1184,1257,1329,1400,1473,1539", "endColumns": "76,75,84,90,76,73,76,77,74,72,74,67,80,72,71,70,72,65,115", "endOffsets": "249,325,410,501,578,652,729,807,882,955,1030,1098,1179,1252,1324,1395,1468,1534,1650"}, "to": {"startLines": "37,38,40,42,43,72,73,172,173,176,177,179,180,181,182,183,186,187,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3397,3474,3616,3768,3859,5860,5934,15415,15493,15758,15831,15985,16053,16134,16207,16279,16619,16692,16758", "endColumns": "76,75,84,90,76,73,76,77,74,72,74,67,80,72,71,70,72,65,115", "endOffsets": "3469,3545,3696,3854,3931,5929,6006,15488,15563,15826,15901,16048,16129,16202,16274,16345,16687,16753,16869"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\886fef481c8ce4a2616ccf22a8cd1328\\transformed\\foundation-release\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,126,207", "endColumns": "70,80,76", "endOffsets": "121,202,279"}, "to": {"startLines": "29,189,190", "startColumns": "4,4,4", "startOffsets": "2662,16874,16955", "endColumns": "70,80,76", "endOffsets": "2728,16950,17027"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\14cf378553861cbecadb791c6e2341a9\\transformed\\core-1.16.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,248,342,436,529,623,719", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "142,243,337,431,524,618,714,815"}, "to": {"startLines": "30,31,32,33,34,35,36,184", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2733,2825,2926,3020,3114,3207,3301,16350", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "2820,2921,3015,3109,3202,3296,3392,16446"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\2fbbcb27eaccaf382f754b7d51fec492\\transformed\\mediarouter-1.7.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,160,251,341,433,528,633,730,816,895,1064,1186,1310,1437,1558,1684,1808,1909,1996,2090,2183,2273,2361,2449,2547,2647,2727,2807,2887,2976,3067,3157,3247,3344,3426", "endColumns": "104,90,89,91,94,104,96,85,78,168,121,123,126,120,125,123,100,86,93,92,89,87,87,97,99,79,79,79,88,90,89,89,96,81,88", "endOffsets": "155,246,336,428,523,628,725,811,890,1059,1181,1305,1432,1553,1679,1803,1904,1991,2085,2178,2268,2356,2444,2542,2642,2722,2802,2882,2971,3062,3152,3242,3339,3421,3510"}, "to": {"startLines": "71,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5755,12060,12151,12241,12333,12428,12533,12630,12716,12795,12964,13086,13210,13337,13458,13584,13708,13809,13896,13990,14083,14173,14261,14349,14447,14547,14627,14707,14787,14876,14967,15057,15147,15244,15326", "endColumns": "104,90,89,91,94,104,96,85,78,168,121,123,126,120,125,123,100,86,93,92,89,87,87,97,99,79,79,79,88,90,89,89,96,81,88", "endOffsets": "5855,12146,12236,12328,12423,12528,12625,12711,12790,12959,13081,13205,13332,13453,13579,13703,13804,13891,13985,14078,14168,14256,14344,14442,14542,14622,14702,14782,14871,14962,15052,15142,15239,15321,15410"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\c9938815eb16e1bbe389a6512658438a\\transformed\\material3-release\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,159,262,366,468,560,648,752,860,963,1078,1160,1256,1340,1429,1535,1649,1750,1860,1968,2076,2192,2299,2400,2504,2610,2695,2790,2895,3004,3094,3192,3290,3400,3515,3615,3706,3779,3869,3958,4051,4134,4216,4308,4388,4470,4568,4662,4755,4850,4934,5030,5126,5223,5331,5411,5503", "endColumns": "103,102,103,101,91,87,103,107,102,114,81,95,83,88,105,113,100,109,107,107,115,106,100,103,105,84,94,104,108,89,97,97,109,114,99,90,72,89,88,92,82,81,91,79,81,97,93,92,94,83,95,95,96,107,79,91,89", "endOffsets": "154,257,361,463,555,643,747,855,958,1073,1155,1251,1335,1424,1530,1644,1745,1855,1963,2071,2187,2294,2395,2499,2605,2690,2785,2890,2999,3089,3187,3285,3395,3510,3610,3701,3774,3864,3953,4046,4129,4211,4303,4383,4465,4563,4657,4750,4845,4929,5025,5121,5218,5326,5406,5498,5588"}, "to": {"startLines": "74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6011,6115,6218,6322,6424,6516,6604,6708,6816,6919,7034,7116,7212,7296,7385,7491,7605,7706,7816,7924,8032,8148,8255,8356,8460,8566,8651,8746,8851,8960,9050,9148,9246,9356,9471,9571,9662,9735,9825,9914,10007,10090,10172,10264,10344,10426,10524,10618,10711,10806,10890,10986,11082,11179,11287,11367,11459", "endColumns": "103,102,103,101,91,87,103,107,102,114,81,95,83,88,105,113,100,109,107,107,115,106,100,103,105,84,94,104,108,89,97,97,109,114,99,90,72,89,88,92,82,81,91,79,81,97,93,92,94,83,95,95,96,107,79,91,89", "endOffsets": "6110,6213,6317,6419,6511,6599,6703,6811,6914,7029,7111,7207,7291,7380,7486,7600,7701,7811,7919,8027,8143,8250,8351,8455,8561,8646,8741,8846,8955,9045,9143,9241,9351,9466,9566,9657,9730,9820,9909,10002,10085,10167,10259,10339,10421,10519,10613,10706,10801,10885,10981,11077,11174,11282,11362,11454,11544"}}]}]}