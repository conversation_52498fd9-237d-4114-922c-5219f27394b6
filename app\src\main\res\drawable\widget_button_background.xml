<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <shape android:shape="oval">
            <gradient
                android:type="radial"
                android:gradientRadius="24dp"
                android:startColor="#60FFFFFF"
                android:endColor="#30FFFFFF" />
            <stroke
                android:width="1dp"
                android:color="#80FFFFFF" />
        </shape>
    </item>
    <item>
        <shape android:shape="oval">
            <gradient
                android:type="radial"
                android:gradientRadius="24dp"
                android:startColor="#40FFFFFF"
                android:endColor="#10FFFFFF" />
            <stroke
                android:width="1dp"
                android:color="#40FFFFFF" />
        </shape>
    </item>
</selector>
