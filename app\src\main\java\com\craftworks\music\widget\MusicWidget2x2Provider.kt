package com.craftworks.music.widget

import android.app.PendingIntent
import android.appwidget.AppWidgetManager
import android.appwidget.AppWidgetProvider
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.util.Log
import android.util.LruCache
import android.widget.RemoteViews
import androidx.core.content.ContextCompat
import androidx.media3.common.MediaMetadata
import androidx.media3.common.Player
import com.craftworks.music.MainActivity
import com.craftworks.music.R
import com.craftworks.music.player.ChoraMediaLibraryService
import com.craftworks.music.player.SongHelper
import kotlinx.coroutines.*
import java.io.InputStream
import java.net.HttpURLConnection
import java.net.URL

class MusicWidget2x2Provider : AppWidgetProvider() {

    companion object {
        const val ACTION_PLAY_PAUSE = "com.craftworks.music.widget.2x2.PLAY_PAUSE"
        const val ACTION_NEXT = "com.craftworks.music.widget.2x2.NEXT"
        const val ACTION_PREVIOUS = "com.craftworks.music.widget.2x2.PREVIOUS"

        // Cache for album art bitmaps
        private val albumArtCache = LruCache<String, Bitmap>(20)
        private val widgetScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
        
        fun updateWidget(context: Context, isPlaying: Boolean, metadata: MediaMetadata?) {
            try {
                val appWidgetManager = AppWidgetManager.getInstance(context)
                val widgetComponent = ComponentName(context, MusicWidget2x2Provider::class.java)
                val widgetIds = appWidgetManager.getAppWidgetIds(widgetComponent)

                for (widgetId in widgetIds) {
                    updateAppWidget(context, appWidgetManager, widgetId, isPlaying, metadata)
                }
                Log.d("WIDGET_2X2", "Widget updated successfully")
            } catch (e: Exception) {
                Log.e("WIDGET_2X2", "Error updating widget: ${e.message}", e)
            }
        }
        
        private fun updateAppWidget(
            context: Context,
            appWidgetManager: AppWidgetManager,
            appWidgetId: Int,
            isPlaying: Boolean,
            metadata: MediaMetadata?
        ) {
            val views = RemoteViews(context.packageName, R.layout.music_widget_2x2)
            
            // Update song info
            val title = metadata?.title?.toString() ?: "No song playing"
            val artist = metadata?.artist?.toString() ?: "Unknown Artist"
            
            views.setTextViewText(R.id.widget_song_title_2x2, title)
            views.setTextViewText(R.id.widget_artist_name_2x2, artist)
            
            // Update play/pause button
            val playPauseIcon = if (isPlaying) R.drawable.ic_pause else R.drawable.ic_play_arrow
            views.setImageViewResource(R.id.widget_play_pause_2x2, playPauseIcon)
            
            // Set up click intents
            setupClickIntents(context, views)
            
            // Update album art
            updateAlbumArt(context, views, metadata, appWidgetManager, appWidgetId)
            
            appWidgetManager.updateAppWidget(appWidgetId, views)
        }
        
        private fun setupClickIntents(context: Context, views: RemoteViews) {
            // Play/Pause button
            val playPauseIntent = Intent(context, MusicWidget2x2Provider::class.java).apply {
                action = ACTION_PLAY_PAUSE
            }
            val playPausePendingIntent = PendingIntent.getBroadcast(
                context, 0, playPauseIntent, 
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            views.setOnClickPendingIntent(R.id.widget_play_pause_2x2, playPausePendingIntent)
            
            // Previous button
            val previousIntent = Intent(context, MusicWidget2x2Provider::class.java).apply {
                action = ACTION_PREVIOUS
            }
            val previousPendingIntent = PendingIntent.getBroadcast(
                context, 1, previousIntent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            views.setOnClickPendingIntent(R.id.widget_previous_2x2, previousPendingIntent)
            
            // Next button
            val nextIntent = Intent(context, MusicWidget2x2Provider::class.java).apply {
                action = ACTION_NEXT
            }
            val nextPendingIntent = PendingIntent.getBroadcast(
                context, 2, nextIntent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            views.setOnClickPendingIntent(R.id.widget_next_2x2, nextPendingIntent)
            
            // Widget click to open app
            val openAppIntent = Intent(context, MainActivity::class.java)
            val openAppPendingIntent = PendingIntent.getActivity(
                context, 3, openAppIntent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            views.setOnClickPendingIntent(R.id.widget_album_art_2x2, openAppPendingIntent)
        }
        
        private fun updateAlbumArt(
            context: Context,
            views: RemoteViews,
            metadata: MediaMetadata?,
            appWidgetManager: AppWidgetManager,
            appWidgetId: Int
        ) {
            // Disable album art to prevent memory issues
            // Widget memory is very limited (max 27MB), so we'll use a simple icon instead
            Log.d("WIDGET_2X2", "Setting default album art to prevent memory issues")
            try {
                views.setImageViewResource(R.id.widget_album_art_2x2, R.drawable.ic_music_note)
            } catch (e: Exception) {
                Log.e("WIDGET_2X2", "Error setting default album art: ${e.message}")
            }
        }

        private suspend fun loadArtworkBitmap(context: Context, artworkUri: Uri): Bitmap? {
            return withContext(Dispatchers.IO) {
                try {
                    when (artworkUri.scheme) {
                        "content" -> {
                            // Handle content URIs (local files)
                            context.contentResolver.openInputStream(artworkUri)?.use { inputStream ->
                                BitmapFactory.decodeStream(inputStream)
                            }
                        }
                        "android.resource" -> {
                            // Handle resource URIs
                            context.contentResolver.openInputStream(artworkUri)?.use { inputStream ->
                                BitmapFactory.decodeStream(inputStream)
                            }
                        }
                        "http", "https" -> {
                            // Handle network URIs
                            val url = URL(artworkUri.toString())
                            val connection = url.openConnection() as HttpURLConnection
                            connection.connectTimeout = 5000
                            connection.readTimeout = 10000
                            connection.doInput = true
                            connection.connect()

                            if (connection.responseCode == HttpURLConnection.HTTP_OK) {
                                connection.inputStream.use { inputStream ->
                                    BitmapFactory.decodeStream(inputStream)
                                }
                            } else {
                                Log.w("WIDGET_2X2", "HTTP error ${connection.responseCode} loading artwork")
                                null
                            }
                        }
                        else -> {
                            Log.w("WIDGET_2X2", "Unsupported URI scheme: ${artworkUri.scheme}")
                            null
                        }
                    }
                } catch (e: Exception) {
                    Log.e("WIDGET_2X2", "Error loading artwork from $artworkUri: ${e.message}")
                    null
                }
            }
        }
    }

    override fun onUpdate(context: Context, appWidgetManager: AppWidgetManager, appWidgetIds: IntArray) {
        Log.d("WIDGET_2X2", "onUpdate called for ${appWidgetIds.size} widgets")
        for (appWidgetId in appWidgetIds) {
            try {
                updateAppWidget(context, appWidgetManager, appWidgetId, false, null)
                Log.d("WIDGET_2X2", "Widget $appWidgetId updated successfully")
            } catch (e: Exception) {
                Log.e("WIDGET_2X2", "Error updating widget $appWidgetId: ${e.message}", e)
            }
        }
    }

    override fun onEnabled(context: Context) {
        super.onEnabled(context)
        Log.d("WIDGET_2X2", "Widget enabled - first widget added")
        // Initialize widget with current playback state if available
        try {
            val mediaController = SongHelper.currentMediaController
            val isPlaying = mediaController?.isPlaying ?: false
            val metadata = mediaController?.currentMediaItem?.mediaMetadata
            updateWidget(context, isPlaying, metadata)
        } catch (e: Exception) {
            Log.e("WIDGET_2X2", "Error initializing widget on enable: ${e.message}", e)
        }
    }

    override fun onDisabled(context: Context) {
        super.onDisabled(context)
        Log.d("WIDGET_2X2", "Widget disabled - last widget removed")
        // Clear any cached resources
        albumArtCache.evictAll()
    }

    override fun onReceive(context: Context, intent: Intent) {
        super.onReceive(context, intent)
        
        when (intent.action) {
            ACTION_PLAY_PAUSE -> {
                try {
                    val controller = SongHelper.currentMediaController
                    val servicePlayer = ChoraMediaLibraryService.getInstance()?.player

                    when {
                        controller != null -> {
                            if (controller.isPlaying) {
                                controller.pause()
                            } else {
                                controller.play()
                            }
                            Log.d("WIDGET_2X2", "Play/Pause action executed via MediaController")
                        }
                        servicePlayer != null -> {
                            if (servicePlayer.isPlaying) {
                                servicePlayer.pause()
                            } else {
                                servicePlayer.play()
                            }
                            Log.d("WIDGET_2X2", "Play/Pause action executed via service player")
                        }
                        else -> {
                            Log.w("WIDGET_2X2", "No player available for play/pause")
                        }
                    }
                } catch (e: Exception) {
                    Log.e("WIDGET_2X2", "Error executing play/pause: ${e.message}", e)
                }
            }
            ACTION_NEXT -> {
                try {
                    SongHelper.currentMediaController?.seekToNext()
                    Log.d("WIDGET_2X2", "Next action executed")
                } catch (e: Exception) {
                    Log.e("WIDGET_2X2", "Error executing next: ${e.message}", e)
                }
            }
            ACTION_PREVIOUS -> {
                try {
                    SongHelper.currentMediaController?.seekToPrevious()
                    Log.d("WIDGET_2X2", "Previous action executed")
                } catch (e: Exception) {
                    Log.e("WIDGET_2X2", "Error executing previous: ${e.message}", e)
                }
            }
        }
    }
}
