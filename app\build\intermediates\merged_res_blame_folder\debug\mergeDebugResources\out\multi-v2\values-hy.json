{"logs": [{"outputFile": "com.craftworks.music.app-mergeDebugResources-3:/values-hy/values-hy.xml", "map": [{"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\64a01e76fd68fe16459fb0e17d50396b\\transformed\\appcompat-1.6.1\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,423,512,618,735,817,897,988,1081,1176,1270,1370,1463,1558,1652,1743,1834,1917,2023,2129,2228,2338,2446,2547,2717,2814", "endColumns": "107,99,109,88,105,116,81,79,90,92,94,93,99,92,94,93,90,90,82,105,105,98,109,107,100,169,96,82", "endOffsets": "208,308,418,507,613,730,812,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1912,2018,2124,2223,2333,2441,2542,2712,2809,2892"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,178", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,423,512,618,735,817,897,988,1081,1176,1270,1370,1463,1558,1652,1743,1834,1917,2023,2129,2228,2338,2446,2547,2717,18185", "endColumns": "107,99,109,88,105,116,81,79,90,92,94,93,99,92,94,93,90,90,82,105,105,98,109,107,100,169,96,82", "endOffsets": "208,308,418,507,613,730,812,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1912,2018,2124,2223,2333,2441,2542,2712,2809,18263"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\886fef481c8ce4a2616ccf22a8cd1328\\transformed\\foundation-release\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,134,219", "endColumns": "78,84,88", "endOffsets": "129,214,303"}, "to": {"startLines": "29,189,190", "startColumns": "4,4,4", "startOffsets": "2814,19201,19286", "endColumns": "78,84,88", "endOffsets": "2888,19281,19370"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\114a5d5301d7649ab21683cbabeecc89\\transformed\\media3-exoplayer-1.7.1\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,190,254,322,403,480,554,631", "endColumns": "71,62,63,67,80,76,73,76,77", "endOffsets": "122,185,249,317,398,475,549,626,704"}, "to": {"startLines": "61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "5805,5877,5940,6004,6072,6153,6230,6304,6381", "endColumns": "71,62,63,67,80,76,73,76,77", "endOffsets": "5872,5935,5999,6067,6148,6225,6299,6376,6454"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\14cf378553861cbecadb791c6e2341a9\\transformed\\core-1.16.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,260,358,457,562,664,775", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "150,255,353,452,557,659,770,871"}, "to": {"startLines": "30,31,32,33,34,35,36,184", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2893,2993,3098,3196,3295,3400,3502,18663", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "2988,3093,3191,3290,3395,3497,3608,18759"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\c3709e4bfd5d4e5c1ed8a624d490cd12\\transformed\\material-release\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "91", "endOffsets": "142"}, "to": {"startLines": "131", "startColumns": "4", "startOffsets": "13123", "endColumns": "91", "endOffsets": "13210"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\493af54b1b0004569d1870cf1ba0af14\\transformed\\media3-session-1.7.1\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,136,244,320,436,539,628,706,790,880,978,1046,1161,1260,1362,1439,1533,1615,1717,1790,1861,1936,2015,2105", "endColumns": "80,107,75,115,102,88,77,83,89,97,67,114,98,101,76,93,81,101,72,70,74,78,89,93", "endOffsets": "131,239,315,431,534,623,701,785,875,973,1041,1156,1255,1357,1434,1528,1610,1712,1785,1856,1931,2010,2100,2194"}, "to": {"startLines": "41,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,132,133,134,135,136,137", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3960,4224,4332,4408,4524,4627,4716,4794,4878,4968,5066,5134,5249,5348,5450,5527,5621,5703,13215,13288,13359,13434,13513,13603", "endColumns": "80,107,75,115,102,88,77,83,89,97,67,114,98,101,76,93,81,101,72,70,74,78,89,93", "endOffsets": "4036,4327,4403,4519,4622,4711,4789,4873,4963,5061,5129,5244,5343,5445,5522,5616,5698,5800,13283,13354,13429,13508,13598,13692"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\2fbbcb27eaccaf382f754b7d51fec492\\transformed\\mediarouter-1.7.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,270,374,480,588,714,820,915,1002,1180,1348,1519,1691,1865,2035,2208,2322,2417,2523,2619,2712,2803,2896,3006,3118,3206,3293,3381,3489,3604,3697,3797,3910,3998", "endColumns": "115,98,103,105,107,125,105,94,86,177,167,170,171,173,169,172,113,94,105,95,92,90,92,109,111,87,86,87,107,114,92,99,112,87,92", "endOffsets": "166,265,369,475,583,709,815,910,997,1175,1343,1514,1686,1860,2030,2203,2317,2412,2518,2614,2707,2798,2891,3001,3113,3201,3288,3376,3484,3599,3692,3792,3905,3993,4086"}, "to": {"startLines": "71,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6547,13697,13796,13900,14006,14114,14240,14346,14441,14528,14706,14874,15045,15217,15391,15561,15734,15848,15943,16049,16145,16238,16329,16422,16532,16644,16732,16819,16907,17015,17130,17223,17323,17436,17524", "endColumns": "115,98,103,105,107,125,105,94,86,177,167,170,171,173,169,172,113,94,105,95,92,90,92,109,111,87,86,87,107,114,92,99,112,87,92", "endOffsets": "6658,13791,13895,14001,14109,14235,14341,14436,14523,14701,14869,15040,15212,15386,15556,15729,15843,15938,16044,16140,16233,16324,16417,16527,16639,16727,16814,16902,17010,17125,17218,17318,17431,17519,17612"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\b12f5ec7860c79fe319ea01d980e4aac\\transformed\\ui-release\\res\\values-hy\\values-hy.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "185,284,366,460,560,643,725,811,906,988,1073,1161,1235,1323,1400,1479,1556,1637,1706", "endColumns": "98,81,93,99,82,81,85,94,81,84,87,73,87,76,78,76,80,68,117", "endOffsets": "279,361,455,555,638,720,806,901,983,1068,1156,1230,1318,1395,1474,1551,1632,1701,1819"}, "to": {"startLines": "37,38,40,42,43,72,73,172,173,176,177,179,180,181,182,183,186,187,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3613,3712,3866,4041,4141,6663,6745,17617,17712,18012,18097,18268,18342,18430,18507,18586,18933,19014,19083", "endColumns": "98,81,93,99,82,81,85,94,81,84,87,73,87,76,78,76,80,68,117", "endOffsets": "3707,3789,3955,4136,4219,6740,6826,17707,17789,18092,18180,18337,18425,18502,18581,18658,19009,19078,19196"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\4f725fd807947c49350c5a696b32f0e9\\transformed\\preference-1.2.1\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,265,346,483,652,736", "endColumns": "71,87,80,136,168,83,80", "endOffsets": "172,260,341,478,647,731,812"}, "to": {"startLines": "39,70,174,175,185,191,192", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3794,6459,17794,17875,18764,19375,19459", "endColumns": "71,87,80,136,168,83,80", "endOffsets": "3861,6542,17870,18007,18928,19454,19535"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\c9938815eb16e1bbe389a6512658438a\\transformed\\material3-release\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,286,401,517,616,718,837,983,1106,1262,1349,1447,1542,1641,1763,1885,1988,2128,2266,2399,2576,2705,2821,2940,3063,3159,3257,3380,3521,3627,3732,3840,3979,4123,4232,4334,4425,4520,4616,4723,4811,4896,5010,5090,5173,5272,5373,5464,5560,5649,5753,5851,5951,6068,6148,6253", "endColumns": "115,114,114,115,98,101,118,145,122,155,86,97,94,98,121,121,102,139,137,132,176,128,115,118,122,95,97,122,140,105,104,107,138,143,108,101,90,94,95,106,87,84,113,79,82,98,100,90,95,88,103,97,99,116,79,104,93", "endOffsets": "166,281,396,512,611,713,832,978,1101,1257,1344,1442,1537,1636,1758,1880,1983,2123,2261,2394,2571,2700,2816,2935,3058,3154,3252,3375,3516,3622,3727,3835,3974,4118,4227,4329,4420,4515,4611,4718,4806,4891,5005,5085,5168,5267,5368,5459,5555,5644,5748,5846,5946,6063,6143,6248,6342"}, "to": {"startLines": "74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6831,6947,7062,7177,7293,7392,7494,7613,7759,7882,8038,8125,8223,8318,8417,8539,8661,8764,8904,9042,9175,9352,9481,9597,9716,9839,9935,10033,10156,10297,10403,10508,10616,10755,10899,11008,11110,11201,11296,11392,11499,11587,11672,11786,11866,11949,12048,12149,12240,12336,12425,12529,12627,12727,12844,12924,13029", "endColumns": "115,114,114,115,98,101,118,145,122,155,86,97,94,98,121,121,102,139,137,132,176,128,115,118,122,95,97,122,140,105,104,107,138,143,108,101,90,94,95,106,87,84,113,79,82,98,100,90,95,88,103,97,99,116,79,104,93", "endOffsets": "6942,7057,7172,7288,7387,7489,7608,7754,7877,8033,8120,8218,8313,8412,8534,8656,8759,8899,9037,9170,9347,9476,9592,9711,9834,9930,10028,10151,10292,10398,10503,10611,10750,10894,11003,11105,11196,11291,11387,11494,11582,11667,11781,11861,11944,12043,12144,12235,12331,12420,12524,12622,12722,12839,12919,13024,13118"}}]}]}