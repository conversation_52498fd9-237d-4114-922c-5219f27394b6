<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- Album Art - Full Background -->
    <ImageView
        android:id="@+id/widget_album_art_2x2"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        android:src="@drawable/ic_music_note" />

    <!-- Gradient Overlay for better text readability -->
    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/widget_gradient_overlay" />

    <!-- Song Info - Top -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_gravity="top"
        android:padding="12dp"
        android:background="@drawable/widget_info_background">

        <TextView
            android:id="@+id/widget_song_title_2x2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="No song playing"
            android:textColor="#FFFFFF"
            android:textSize="14sp"
            android:textStyle="bold"
            android:maxLines="1"
            android:ellipsize="end"
            android:shadowColor="#80000000"
            android:shadowDx="1"
            android:shadowDy="1"
            android:shadowRadius="2" />

        <TextView
            android:id="@+id/widget_artist_name_2x2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Unknown Artist"
            android:textColor="#E0FFFFFF"
            android:textSize="12sp"
            android:maxLines="1"
            android:ellipsize="end"
            android:layout_marginTop="2dp"
            android:shadowColor="#80000000"
            android:shadowDx="1"
            android:shadowDy="1"
            android:shadowRadius="2" />

    </LinearLayout>

    <!-- Control Buttons - Bottom Center -->
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_gravity="bottom|center_horizontal"
        android:layout_marginBottom="16dp"
        android:background="@drawable/widget_controls_background"
        android:padding="8dp">

        <ImageButton
            android:id="@+id/widget_previous_2x2"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:background="@drawable/widget_transparent_button"
            android:src="@drawable/ic_skip_previous"
            android:contentDescription="Previous"
            android:scaleType="centerInside"
            android:layout_marginEnd="8dp"
            android:tint="#FFFFFF" />

        <ImageButton
            android:id="@+id/widget_play_pause_2x2"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="@drawable/widget_transparent_button"
            android:src="@drawable/ic_play_arrow"
            android:contentDescription="Play/Pause"
            android:scaleType="centerInside"
            android:layout_marginHorizontal="8dp"
            android:tint="#FFFFFF" />

        <ImageButton
            android:id="@+id/widget_next_2x2"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:background="@drawable/widget_transparent_button"
            android:src="@drawable/ic_skip_next"
            android:contentDescription="Next"
            android:scaleType="centerInside"
            android:layout_marginStart="8dp"
            android:tint="#FFFFFF" />

    </LinearLayout>

</FrameLayout>
