{"logs": [{"outputFile": "com.craftworks.music.app-mergeDebugResources-3:/values-ky/values-ky.xml", "map": [{"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\b12f5ec7860c79fe319ea01d980e4aac\\transformed\\ui-release\\res\\values-ky\\values-ky.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "194,287,371,481,581,666,748,846,935,1020,1105,1192,1265,1352,1426,1499,1572,1651,1719", "endColumns": "92,83,109,99,84,81,97,88,84,84,86,72,86,73,72,72,78,67,117", "endOffsets": "282,366,476,576,661,743,841,930,1015,1100,1187,1260,1347,1421,1494,1567,1646,1714,1832"}, "to": {"startLines": "37,38,40,42,43,72,73,172,173,176,177,179,180,181,182,183,186,187,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3634,3727,3882,4066,4166,6748,6830,17849,17938,18243,18328,18497,18570,18657,18731,18804,19147,19226,19294", "endColumns": "92,83,109,99,84,81,97,88,84,84,86,72,86,73,72,72,78,67,117", "endOffsets": "3722,3806,3987,4161,4246,6825,6923,17933,18018,18323,18410,18565,18652,18726,18799,18872,19221,19289,19407"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\4f725fd807947c49350c5a696b32f0e9\\transformed\\preference-1.2.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,268,347,488,657,738", "endColumns": "70,91,78,140,168,80,78", "endOffsets": "171,263,342,483,652,733,812"}, "to": {"startLines": "39,70,174,175,185,191,192", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3811,6526,18023,18102,18978,19615,19696", "endColumns": "70,91,78,140,168,80,78", "endOffsets": "3877,6613,18097,18238,19142,19691,19770"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\64a01e76fd68fe16459fb0e17d50396b\\transformed\\appcompat-1.6.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,325,437,522,627,744,823,901,992,1085,1180,1274,1374,1467,1562,1657,1748,1839,1920,2026,2131,2229,2336,2439,2554,2715,2817", "endColumns": "110,108,111,84,104,116,78,77,90,92,94,93,99,92,94,94,90,90,80,105,104,97,106,102,114,160,101,81", "endOffsets": "211,320,432,517,622,739,818,896,987,1080,1175,1269,1369,1462,1557,1652,1743,1834,1915,2021,2126,2224,2331,2434,2549,2710,2812,2894"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,178", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,325,437,522,627,744,823,901,992,1085,1180,1274,1374,1467,1562,1657,1748,1839,1920,2026,2131,2229,2336,2439,2554,2715,18415", "endColumns": "110,108,111,84,104,116,78,77,90,92,94,93,99,92,94,94,90,90,80,105,104,97,106,102,114,160,101,81", "endOffsets": "211,320,432,517,622,739,818,896,987,1080,1175,1269,1369,1462,1557,1652,1743,1834,1915,2021,2126,2224,2331,2434,2549,2710,2812,18492"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\886fef481c8ce4a2616ccf22a8cd1328\\transformed\\foundation-release\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,143,242", "endColumns": "87,98,103", "endOffsets": "138,237,341"}, "to": {"startLines": "29,189,190", "startColumns": "4,4,4", "startOffsets": "2817,19412,19511", "endColumns": "87,98,103", "endOffsets": "2900,19506,19610"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\14cf378553861cbecadb791c6e2341a9\\transformed\\core-1.16.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,360,467,569,673,784", "endColumns": "99,101,102,106,101,103,110,100", "endOffsets": "150,252,355,462,564,668,779,880"}, "to": {"startLines": "30,31,32,33,34,35,36,184", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2905,3005,3107,3210,3317,3419,3523,18877", "endColumns": "99,101,102,106,101,103,110,100", "endOffsets": "3000,3102,3205,3312,3414,3518,3629,18973"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\2fbbcb27eaccaf382f754b7d51fec492\\transformed\\mediarouter-1.7.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,185,295,409,526,646,758,870,969,1061,1241,1408,1580,1753,1922,2095,2264,2383,2481,2594,2690,2788,2880,2980,3085,3198,3284,3369,3455,3568,3676,3767,3865,3967,4054", "endColumns": "129,109,113,116,119,111,111,98,91,179,166,171,172,168,172,168,118,97,112,95,97,91,99,104,112,85,84,85,112,107,90,97,101,86,95", "endOffsets": "180,290,404,521,641,753,865,964,1056,1236,1403,1575,1748,1917,2090,2259,2378,2476,2589,2685,2783,2875,2975,3080,3193,3279,3364,3450,3563,3671,3762,3860,3962,4049,4145"}, "to": {"startLines": "71,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6618,13884,13994,14108,14225,14345,14457,14569,14668,14760,14940,15107,15279,15452,15621,15794,15963,16082,16180,16293,16389,16487,16579,16679,16784,16897,16983,17068,17154,17267,17375,17466,17564,17666,17753", "endColumns": "129,109,113,116,119,111,111,98,91,179,166,171,172,168,172,168,118,97,112,95,97,91,99,104,112,85,84,85,112,107,90,97,101,86,95", "endOffsets": "6743,13989,14103,14220,14340,14452,14564,14663,14755,14935,15102,15274,15447,15616,15789,15958,16077,16175,16288,16384,16482,16574,16674,16779,16892,16978,17063,17149,17262,17370,17461,17559,17661,17748,17844"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\114a5d5301d7649ab21683cbabeecc89\\transformed\\media3-exoplayer-1.7.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,191,262,333,420,491,578,662", "endColumns": "70,64,70,70,86,70,86,83,80", "endOffsets": "121,186,257,328,415,486,573,657,738"}, "to": {"startLines": "61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "5838,5909,5974,6045,6116,6203,6274,6361,6445", "endColumns": "70,64,70,70,86,70,86,83,80", "endOffsets": "5904,5969,6040,6111,6198,6269,6356,6440,6521"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\493af54b1b0004569d1870cf1ba0af14\\transformed\\media3-session-1.7.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,247,329,419,508,600,679,787,885,970,1039,1148,1235,1328,1421,1535,1615,1716,1787,1856,1937,2022,2112", "endColumns": "73,117,81,89,88,91,78,107,97,84,68,108,86,92,92,113,79,100,70,68,80,84,89,93", "endOffsets": "124,242,324,414,503,595,674,782,880,965,1034,1143,1230,1323,1416,1530,1610,1711,1782,1851,1932,2017,2107,2201"}, "to": {"startLines": "41,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,132,133,134,135,136,137", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3992,4251,4369,4451,4541,4630,4722,4801,4909,5007,5092,5161,5270,5357,5450,5543,5657,5737,13394,13465,13534,13615,13700,13790", "endColumns": "73,117,81,89,88,91,78,107,97,84,68,108,86,92,92,113,79,100,70,68,80,84,89,93", "endOffsets": "4061,4364,4446,4536,4625,4717,4796,4904,5002,5087,5156,5265,5352,5445,5538,5652,5732,5833,13460,13529,13610,13695,13785,13879"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\c9938815eb16e1bbe389a6512658438a\\transformed\\material3-release\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,178,295,409,534,634,732,847,983,1124,1280,1364,1462,1554,1651,1767,1886,1989,2125,2259,2396,2571,2700,2817,2937,3058,3151,3249,3371,3508,3611,3736,3841,3975,4114,4223,4325,4401,4500,4604,4718,4804,4889,5001,5090,5174,5274,5375,5471,5568,5655,5766,5865,5965,6113,6203,6322", "endColumns": "122,116,113,124,99,97,114,135,140,155,83,97,91,96,115,118,102,135,133,136,174,128,116,119,120,92,97,121,136,102,124,104,133,138,108,101,75,98,103,113,85,84,111,88,83,99,100,95,96,86,110,98,99,147,89,118,107", "endOffsets": "173,290,404,529,629,727,842,978,1119,1275,1359,1457,1549,1646,1762,1881,1984,2120,2254,2391,2566,2695,2812,2932,3053,3146,3244,3366,3503,3606,3731,3836,3970,4109,4218,4320,4396,4495,4599,4713,4799,4884,4996,5085,5169,5269,5370,5466,5563,5650,5761,5860,5960,6108,6198,6317,6425"}, "to": {"startLines": "74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6928,7051,7168,7282,7407,7507,7605,7720,7856,7997,8153,8237,8335,8427,8524,8640,8759,8862,8998,9132,9269,9444,9573,9690,9810,9931,10024,10122,10244,10381,10484,10609,10714,10848,10987,11096,11198,11274,11373,11477,11591,11677,11762,11874,11963,12047,12147,12248,12344,12441,12528,12639,12738,12838,12986,13076,13195", "endColumns": "122,116,113,124,99,97,114,135,140,155,83,97,91,96,115,118,102,135,133,136,174,128,116,119,120,92,97,121,136,102,124,104,133,138,108,101,75,98,103,113,85,84,111,88,83,99,100,95,96,86,110,98,99,147,89,118,107", "endOffsets": "7046,7163,7277,7402,7502,7600,7715,7851,7992,8148,8232,8330,8422,8519,8635,8754,8857,8993,9127,9264,9439,9568,9685,9805,9926,10019,10117,10239,10376,10479,10604,10709,10843,10982,11091,11193,11269,11368,11472,11586,11672,11757,11869,11958,12042,12142,12243,12339,12436,12523,12634,12733,12833,12981,13071,13190,13298"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\c3709e4bfd5d4e5c1ed8a624d490cd12\\transformed\\material-release\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "90", "endOffsets": "141"}, "to": {"startLines": "131", "startColumns": "4", "startOffsets": "13303", "endColumns": "90", "endOffsets": "13389"}}]}]}