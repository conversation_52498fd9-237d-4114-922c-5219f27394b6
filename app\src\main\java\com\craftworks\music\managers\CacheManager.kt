package com.craftworks.music.managers

import android.content.Context
import android.util.Log
import androidx.media3.common.MediaMetadata
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream
import java.net.URL
import java.security.MessageDigest

object CacheManager {
    private const val TAG = "CacheManager"
    private const val CACHE_DIR_NAME = "music_cache"
    
    private fun getCacheDirectory(context: Context): File {
        return File(context.cacheDir, CACHE_DIR_NAME).apply {
            if (!exists()) mkdirs()
        }
    }
    
    private fun generateCacheKey(url: String): String {
        val md = MessageDigest.getInstance("MD5")
        val hashBytes = md.digest(url.toByteArray())
        return hashBytes.joinToString("") { "%02x".format(it) }
    }
    
    private fun getCachedFile(context: Context, url: String, format: String): File {
        val cacheKey = generateCacheKey(url)
        return File(getCacheDirectory(context), "$cacheKey.$format")
    }
    
    suspend fun getCachedSongPath(context: Context, metadata: MediaMetadata): String? {
        return withContext(Dispatchers.IO) {
            try {
                val mediaUrl = metadata.extras?.getString("mediaUrl") ?: return@withContext null
                val format = metadata.extras?.getString("format") ?: "mp3"
                val cachedFile = getCachedFile(context, mediaUrl, format)
                
                if (cachedFile.exists() && cachedFile.length() > 0) {
                    Log.d(TAG, "Found cached file for ${metadata.title}: ${cachedFile.absolutePath}")
                    cachedFile.absolutePath
                } else {
                    null
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error checking cached song: ${e.message}")
                null
            }
        }
    }
    
    suspend fun cacheSong(context: Context, metadata: MediaMetadata, mediaUrl: String): String? {
        return withContext(Dispatchers.IO) {
            try {
                val settingsManager = SettingsManager(context)
                val autoCacheEnabled = settingsManager.autoCacheEnabledFlow.first()
                
                if (!autoCacheEnabled) {
                    Log.d(TAG, "Auto cache is disabled")
                    return@withContext null
                }
                
                val format = metadata.extras?.getString("format") ?: "mp3"
                val cachedFile = getCachedFile(context, mediaUrl, format)
                
                // Check if already cached
                if (cachedFile.exists() && cachedFile.length() > 0) {
                    Log.d(TAG, "Song already cached: ${metadata.title}")
                    return@withContext cachedFile.absolutePath
                }
                
                // Check cache size limit
                val cacheSizeLimit = settingsManager.cacheSizeLimitFlow.first()
                val currentCacheSize = getCacheSize(context)
                
                Log.d(TAG, "Caching song: ${metadata.title}")
                Log.d(TAG, "Current cache size: ${currentCacheSize / (1024 * 1024)} MB")
                Log.d(TAG, "Cache size limit: ${cacheSizeLimit / (1024 * 1024)} MB")
                
                // Download and cache the file
                val url = URL(mediaUrl)
                val inputStream: InputStream = url.openStream()
                val fileOutputStream = FileOutputStream(cachedFile)
                
                val buffer = ByteArray(8192)
                var totalBytesRead = 0L
                var bytesRead: Int
                
                while (inputStream.read(buffer).also { bytesRead = it } != -1) {
                    fileOutputStream.write(buffer, 0, bytesRead)
                    totalBytesRead += bytesRead
                    
                    // Check if we're exceeding cache limit
                    if (currentCacheSize + totalBytesRead > cacheSizeLimit) {
                        Log.w(TAG, "Cache size limit would be exceeded, stopping cache")
                        fileOutputStream.close()
                        inputStream.close()
                        cachedFile.delete()
                        return@withContext null
                    }
                }
                
                fileOutputStream.close()
                inputStream.close()
                
                Log.d(TAG, "Successfully cached song: ${metadata.title}")
                cachedFile.absolutePath
                
            } catch (e: Exception) {
                Log.e(TAG, "Error caching song ${metadata.title}: ${e.message}")
                null
            }
        }
    }
    
    fun getCacheSize(context: Context): Long {
        return try {
            val cacheDir = getCacheDirectory(context)
            calculateDirectorySize(cacheDir)
        } catch (e: Exception) {
            Log.e(TAG, "Error calculating cache size: ${e.message}")
            0L
        }
    }
    
    private fun calculateDirectorySize(directory: File): Long {
        var size = 0L
        if (directory.exists() && directory.isDirectory) {
            directory.listFiles()?.forEach { file ->
                size += if (file.isDirectory) {
                    calculateDirectorySize(file)
                } else {
                    file.length()
                }
            }
        }
        return size
    }
    
    suspend fun clearCache(context: Context): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val cacheDir = getCacheDirectory(context)
                val deleted = cacheDir.deleteRecursively()
                if (deleted) {
                    cacheDir.mkdirs() // Recreate the directory
                    Log.d(TAG, "Cache cleared successfully")
                } else {
                    Log.e(TAG, "Failed to clear cache")
                }
                deleted
            } catch (e: Exception) {
                Log.e(TAG, "Error clearing cache: ${e.message}")
                false
            }
        }
    }
    
    suspend fun cleanupOldCache(context: Context) {
        withContext(Dispatchers.IO) {
            try {
                val settingsManager = SettingsManager(context)
                val cacheSizeLimit = settingsManager.cacheSizeLimitFlow.first()
                val cacheDir = getCacheDirectory(context)
                
                val currentSize = calculateDirectorySize(cacheDir)
                if (currentSize <= cacheSizeLimit) {
                    return@withContext
                }
                
                Log.d(TAG, "Cache size ($currentSize bytes) exceeds limit ($cacheSizeLimit bytes), cleaning up...")
                
                // Get all cached files sorted by last modified (oldest first)
                val cachedFiles = cacheDir.listFiles()?.filter { it.isFile }?.sortedBy { it.lastModified() } ?: return@withContext
                
                var sizeToDelete = currentSize - cacheSizeLimit
                for (file in cachedFiles) {
                    if (sizeToDelete <= 0) break
                    
                    val fileSize = file.length()
                    if (file.delete()) {
                        sizeToDelete -= fileSize
                        Log.d(TAG, "Deleted cached file: ${file.name}")
                    }
                }
                
                Log.d(TAG, "Cache cleanup completed")
            } catch (e: Exception) {
                Log.e(TAG, "Error during cache cleanup: ${e.message}")
            }
        }
    }
    
    fun getCachedSongs(context: Context): List<File> {
        return try {
            val cacheDir = getCacheDirectory(context)
            cacheDir.listFiles()?.filter { it.isFile && it.length() > 0 }?.toList() ?: emptyList()
        } catch (e: Exception) {
            Log.e(TAG, "Error getting cached songs: ${e.message}")
            emptyList()
        }
    }
    
    fun formatCacheSize(sizeInBytes: Long): String {
        return when {
            sizeInBytes < 1024 -> "$sizeInBytes B"
            sizeInBytes < 1024 * 1024 -> "${sizeInBytes / 1024} KB"
            sizeInBytes < 1024 * 1024 * 1024 -> "${sizeInBytes / (1024 * 1024)} MB"
            else -> "${sizeInBytes / (1024 * 1024 * 1024)} GB"
        }
    }
}
