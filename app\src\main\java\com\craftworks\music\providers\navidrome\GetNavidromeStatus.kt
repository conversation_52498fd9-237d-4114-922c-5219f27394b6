package com.craftworks.music.providers.navidrome

import android.util.Log
import androidx.compose.runtime.mutableStateOf
import com.craftworks.music.data.NavidromeProvider
import com.craftworks.music.managers.NavidromeManager
import com.gitlab.mvysny.konsumexml.konsumeXml

var navidromeStatus = mutableStateOf("")

suspend fun getNavidromeStatus(server: NavidromeProvider){
    try {
        Log.d("NAVIDROME_LOGIN", "Testing connection to: ${server.url}")
        navidromeStatus.value = "Connecting..."

        NavidromeManager.addServer(server)
        sendNavidromeGETRequest("ping.view?", true)

        Log.d("NAVIDROME_LOGIN", "Connection test completed with status: ${navidromeStatus.value}")
    } catch (e: Exception) {
        Log.e("NAVIDROME_LOGIN", "Login test failed: ${e.message}", e)
        navidromeStatus.value = "Connection failed: ${e.message}"
    } finally {
        NavidromeManager.removeServer(server.id)
    }
}

fun parseNavidromeStatusXML(response: String){
    try {
        Log.d("NAVIDROME_XML", "Parsing XML response: ${response.take(200)}...")

        // Avoid crashing by removing some useless tags and validating response
        if (response.isBlank()) {
            navidromeStatus.value = "Empty response from server"
            return
        }

        val newResponse = response
            .replace("xmlns=\"http://subsonic.org/restapi\" ", "")
            .trim()

        if (!newResponse.contains("subsonic-response")) {
            navidromeStatus.value = "Invalid server response"
            return
        }

        newResponse.konsumeXml().apply {
            child("subsonic-response"){
                val status = attributes.getValue("status")
                Log.d("NAVIDROME_XML", "Response status: $status")

                if (status == "failed"){
                    childOrNull("error"){
                        val errorCode = attributes.getValueOrNull("code") ?: "Unknown"
                        val errorMessage = attributes.getValueOrNull("message") ?: "Unknown error"
                        Log.d("NAVIDROME_XML", "Navidrome Error Code: $errorCode, Message: $errorMessage")
                        navidromeStatus.value = "Error $errorCode: $errorMessage"
                    }
                }
                else {
                    navidromeStatus.value = "ok"
                    Log.d("NAVIDROME_XML", "Login successful")
                }

                skipContents()
            }
        }
    } catch (e: Exception) {
        Log.e("NAVIDROME_XML", "Error parsing XML response: ${e.message}", e)
        navidromeStatus.value = "Failed to parse server response"
    }
}