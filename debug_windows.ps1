# Windows PowerShell 音乐播放调试脚本

Write-Host "🎵 音乐播放功能调试 (Windows)" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green

# 清除日志
Write-Host "🧹 清除旧日志..." -ForegroundColor Yellow
adb logcat -c

# 启动应用
Write-Host "🚀 启动应用..." -ForegroundColor Yellow
adb shell am start -n com.craftworks.music/.MainActivity

# 等待应用启动
Write-Host "⏳ 等待应用启动（10秒）..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# 检查服务启动
Write-Host "🔍 检查服务启动状态..." -ForegroundColor Yellow
$serviceLogs = adb logcat -d | Select-String "MUSIC_SERVICE.*SERVICE CREATED"
if ($serviceLogs.Count -eq 0) {
    Write-Host "❌ 服务未启动" -ForegroundColor Red
    Write-Host "📋 相关日志：" -ForegroundColor Yellow
    adb logcat -d | Select-String "MUSIC_SERVICE|STARTUP|ERROR" | Select-Object -Last 20
} else {
    Write-Host "✅ 服务启动成功" -ForegroundColor Green
    $serviceLogs | ForEach-Object { Write-Host $_.Line -ForegroundColor Cyan }
}

# 检查MediaController连接
Write-Host "🔗 检查MediaController连接..." -ForegroundColor Yellow
$controllerLogs = adb logcat -d | Select-String "MediaController.*connected.*true"
if ($controllerLogs.Count -eq 0) {
    Write-Host "⚠️  MediaController可能未连接" -ForegroundColor Yellow
    Write-Host "📋 MediaController日志：" -ForegroundColor Yellow
    adb logcat -d | Select-String "MEDIA_CONTROLLER" | Select-Object -Last 10 | ForEach-Object { Write-Host $_.Line -ForegroundColor Cyan }
} else {
    Write-Host "✅ MediaController连接成功" -ForegroundColor Green
    $controllerLogs | ForEach-Object { Write-Host $_.Line -ForegroundColor Cyan }
}

# 检查ExoPlayer初始化
Write-Host "🎧 检查ExoPlayer初始化..." -ForegroundColor Yellow
$playerLogs = adb logcat -d | Select-String "ExoPlayer.*initialized"
if ($playerLogs.Count -eq 0) {
    Write-Host "⚠️  ExoPlayer可能未正确初始化" -ForegroundColor Yellow
} else {
    Write-Host "✅ ExoPlayer初始化成功" -ForegroundColor Green
    $playerLogs | ForEach-Object { Write-Host $_.Line -ForegroundColor Cyan }
}

Write-Host ""
Write-Host "🎵 现在请在应用中点击一首歌曲..." -ForegroundColor Green
Write-Host "⏳ 等待播放尝试（30秒）..." -ForegroundColor Yellow

# 监控播放日志30秒
$timeout = 30
$startTime = Get-Date
while (((Get-Date) - $startTime).TotalSeconds -lt $timeout) {
    $playLogs = adb logcat -d | Select-String "SONG_HELPER.*PLAY CALLED|Player.*isPlaying.*true|PLAYBACK.*started" | Select-Object -Last 5
    if ($playLogs.Count -gt 0) {
        Write-Host "🎉 发现播放活动：" -ForegroundColor Green
        $playLogs | ForEach-Object { Write-Host $_.Line -ForegroundColor Cyan }
        break
    }
    Start-Sleep -Seconds 2
}

# 最终状态检查
Write-Host ""
Write-Host "📋 最终状态检查：" -ForegroundColor Yellow
Write-Host "--- 播放相关日志 ---" -ForegroundColor Yellow
adb logcat -d | Select-String "SONG_HELPER|SONGS_SCREEN" | Select-Object -Last 10 | ForEach-Object { Write-Host $_.Line -ForegroundColor White }

Write-Host ""
Write-Host "--- 错误日志 ---" -ForegroundColor Red
adb logcat -d | Select-String "ERROR|Error|error" | Select-Object -Last 5 | ForEach-Object { Write-Host $_.Line -ForegroundColor Red }

Write-Host ""
Write-Host "🔍 调试完成。如果没有看到播放活动，请检查上述日志。" -ForegroundColor Green
