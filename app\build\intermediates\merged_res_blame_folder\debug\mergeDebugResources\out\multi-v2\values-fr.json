{"logs": [{"outputFile": "com.craftworks.music.app-mergeDebugResources-3:/values-fr/values-fr.xml", "map": [{"source": "E:\\NAVIchora\\Chora\\app\\src\\main\\res\\values-fr\\strings.xml", "from": {"startLines": "17,23,10,96,22,20,19,14,11,12,13,15,18,4,5,82,81,80,37,46,83,44,26,38,43,93,84,36,45,30,49,34,32,33,51,47,97,41,39,40,53,55,56,27,87,90,86,88,89,85,64,65,69,75,71,70,72,91,74,79,78,76,77,92,67,68,73,28,94,95,25,60,8,7,62,59,61,6,3,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "737,1021,391,5722,973,891,836,600,437,490,546,647,785,128,171,4706,4654,4602,1875,2497,4757,2316,1175,1948,2249,5580,4834,1778,2387,1456,2667,1685,1544,1611,2760,2573,5782,2132,2007,2070,2866,2955,3032,1241,5051,5312,4964,5152,5227,4894,3467,3528,3752,4215,3900,3815,3976,5435,4120,4523,4444,4303,4364,5507,3612,3678,4057,1374,5628,5675,1125,3252,317,266,3365,3190,3304,217,86,3138", "endColumns": "46,68,44,58,46,52,53,45,51,54,52,51,49,41,44,49,50,50,71,74,75,69,64,57,65,46,58,95,108,58,65,66,65,72,78,67,77,87,61,60,51,75,81,131,99,121,85,73,83,68,59,55,61,86,74,83,79,70,93,77,77,59,78,71,64,72,61,56,45,45,48,50,47,49,67,60,59,47,40,50", "endOffsets": "779,1085,431,5776,1015,939,885,641,484,540,594,694,830,165,211,4751,4700,4648,1942,2567,4828,2381,1235,2001,2310,5622,4888,1869,2491,1510,2728,1747,1605,1679,2834,2636,5855,2215,2064,2126,2913,3026,3109,1368,5146,5429,5045,5221,5306,4958,3522,3579,3809,4297,3970,3894,4051,5501,4209,4596,4517,4358,4438,5574,3672,3746,4114,1426,5669,5716,1169,3298,360,311,3428,3246,3359,260,122,3184"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,209,247,249,250,253,254,257,259,272", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,152,221,266,325,372,425,479,525,577,632,685,737,787,829,874,924,975,1026,1098,1173,1249,1319,1384,1442,1508,1555,1614,1710,1819,1878,1944,2011,2077,2150,2229,2297,2375,2463,2525,2586,2638,2714,2796,2928,3028,3150,3236,3310,3394,3463,3523,3579,3641,3728,3803,3887,3967,4038,4132,4210,4288,4348,4427,4499,4564,4637,4699,4756,4802,4848,18816,23169,23359,23409,23643,23704,23927,24060,25217", "endColumns": "46,68,44,58,46,52,53,45,51,54,52,51,49,41,44,49,50,50,71,74,75,69,64,57,65,46,58,95,108,58,65,66,65,72,78,67,77,87,61,60,51,75,81,131,99,121,85,73,83,68,59,55,61,86,74,83,79,70,93,77,77,59,78,71,64,72,61,56,45,45,48,50,47,49,67,60,59,47,40,50", "endOffsets": "147,216,261,320,367,420,474,520,572,627,680,732,782,824,869,919,970,1021,1093,1168,1244,1314,1379,1437,1503,1550,1609,1705,1814,1873,1939,2006,2072,2145,2224,2292,2370,2458,2520,2581,2633,2709,2791,2923,3023,3145,3231,3305,3389,3458,3518,3574,3636,3723,3798,3882,3962,4033,4127,4205,4283,4343,4422,4494,4559,4632,4694,4751,4797,4843,4892,18862,23212,23404,23472,23699,23759,23970,24096,25263"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\2fbbcb27eaccaf382f754b7d51fec492\\transformed\\mediarouter-1.7.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,178,273,370,470,572,698,811,914,1000,1179,1364,1550,1739,1926,2114,2300,2419,2522,2632,2729,2824,2919,3015,3134,3261,3344,3429,3512,3617,3721,3815,3920,4040,4127", "endColumns": "122,94,96,99,101,125,112,102,85,178,184,185,188,186,187,185,118,102,109,96,94,94,95,118,126,82,84,82,104,103,93,104,119,86,95", "endOffsets": "173,268,365,465,567,693,806,909,995,1174,1359,1545,1734,1921,2109,2295,2414,2517,2627,2724,2819,2914,3010,3129,3256,3339,3424,3507,3612,3716,3810,3915,4035,4122,4218"}, "to": {"startLines": "142,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "11510,18867,18962,19059,19159,19261,19387,19500,19603,19689,19868,20053,20239,20428,20615,20803,20989,21108,21211,21321,21418,21513,21608,21704,21823,21950,22033,22118,22201,22306,22410,22504,22609,22729,22816", "endColumns": "122,94,96,99,101,125,112,102,85,178,184,185,188,186,187,185,118,102,109,96,94,94,95,118,126,82,84,82,104,103,93,104,119,86,95", "endOffsets": "11628,18957,19054,19154,19256,19382,19495,19598,19684,19863,20048,20234,20423,20610,20798,20984,21103,21206,21316,21413,21508,21603,21699,21818,21945,22028,22113,22196,22301,22405,22499,22604,22724,22811,22907"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\c3709e4bfd5d4e5c1ed8a624d490cd12\\transformed\\material-release\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "88", "endOffsets": "139"}, "to": {"startLines": "202", "startColumns": "4", "startOffsets": "18226", "endColumns": "88", "endOffsets": "18310"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\c9938815eb16e1bbe389a6512658438a\\transformed\\material3-release\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,293,415,536,635,729,841,985,1104,1251,1335,1435,1536,1637,1758,1885,1990,2140,2286,2416,2608,2734,2852,2975,3108,3210,3315,3439,3564,3666,3773,3878,4023,4175,4284,4393,4480,4573,4668,4788,4879,4965,5072,5152,5237,5339,5451,5549,5649,5737,5853,5954,6057,6189,6269,6379", "endColumns": "119,117,121,120,98,93,111,143,118,146,83,99,100,100,120,126,104,149,145,129,191,125,117,122,132,101,104,123,124,101,106,104,144,151,108,108,86,92,94,119,90,85,106,79,84,101,111,97,99,87,115,100,102,131,79,109,97", "endOffsets": "170,288,410,531,630,724,836,980,1099,1246,1330,1430,1531,1632,1753,1880,1985,2135,2281,2411,2603,2729,2847,2970,3103,3205,3310,3434,3559,3661,3768,3873,4018,4170,4279,4388,4475,4568,4663,4783,4874,4960,5067,5147,5232,5334,5446,5544,5644,5732,5848,5949,6052,6184,6264,6374,6472"}, "to": {"startLines": "145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "11804,11924,12042,12164,12285,12384,12478,12590,12734,12853,13000,13084,13184,13285,13386,13507,13634,13739,13889,14035,14165,14357,14483,14601,14724,14857,14959,15064,15188,15313,15415,15522,15627,15772,15924,16033,16142,16229,16322,16417,16537,16628,16714,16821,16901,16986,17088,17200,17298,17398,17486,17602,17703,17806,17938,18018,18128", "endColumns": "119,117,121,120,98,93,111,143,118,146,83,99,100,100,120,126,104,149,145,129,191,125,117,122,132,101,104,123,124,101,106,104,144,151,108,108,86,92,94,119,90,85,106,79,84,101,111,97,99,87,115,100,102,131,79,109,97", "endOffsets": "11919,12037,12159,12280,12379,12473,12585,12729,12848,12995,13079,13179,13280,13381,13502,13629,13734,13884,14030,14160,14352,14478,14596,14719,14852,14954,15059,15183,15308,15410,15517,15622,15767,15919,16028,16137,16224,16317,16412,16532,16623,16709,16816,16896,16981,17083,17195,17293,17393,17481,17597,17698,17801,17933,18013,18123,18221"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\b12f5ec7860c79fe319ea01d980e4aac\\transformed\\ui-release\\res\\values-fr\\values-fr.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "191,290,378,478,578,665,744,836,928,1015,1096,1181,1257,1342,1417,1495,1569,1647,1716", "endColumns": "98,87,99,99,86,78,91,91,86,80,84,75,84,74,77,73,77,68,121", "endOffsets": "285,373,473,573,660,739,831,923,1010,1091,1176,1252,1337,1412,1490,1564,1642,1711,1833"}, "to": {"startLines": "108,109,111,113,114,143,144,244,245,251,252,256,258,260,261,262,265,266,267", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8451,8550,8708,8890,8990,11633,11712,22912,23004,23477,23558,23851,23975,24101,24176,24254,24598,24676,24745", "endColumns": "98,87,99,99,86,78,91,91,86,80,84,75,84,74,77,73,77,68,121", "endOffsets": "8545,8633,8803,8985,9072,11707,11799,22999,23086,23553,23638,23922,24055,24171,24249,24323,24671,24740,24862"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\114a5d5301d7649ab21683cbabeecc89\\transformed\\media3-exoplayer-1.7.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,194,263,335,418,495,592,685", "endColumns": "73,64,68,71,82,76,96,92,82", "endOffsets": "124,189,258,330,413,490,587,680,763"}, "to": {"startLines": "132,133,134,135,136,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "10700,10774,10839,10908,10980,11063,11140,11237,11330", "endColumns": "73,64,68,71,82,76,96,92,82", "endOffsets": "10769,10834,10903,10975,11058,11135,11232,11325,11408"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\4f725fd807947c49350c5a696b32f0e9\\transformed\\preference-1.2.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,272,350,492,661,747", "endColumns": "69,96,77,141,168,85,79", "endOffsets": "170,267,345,487,656,742,822"}, "to": {"startLines": "110,141,246,248,264,270,271", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "8638,11413,23091,23217,24429,25051,25137", "endColumns": "69,96,77,141,168,85,79", "endOffsets": "8703,11505,23164,23354,24593,25132,25212"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\64a01e76fd68fe16459fb0e17d50396b\\transformed\\appcompat-1.6.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,331,441,523,629,759,837,913,1004,1097,1195,1290,1390,1483,1576,1671,1762,1853,1939,2049,2160,2263,2374,2482,2589,2748,2847", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,326,436,518,624,754,832,908,999,1092,1190,1285,1385,1478,1571,1666,1757,1848,1934,2044,2155,2258,2369,2477,2584,2743,2842,2929"}, "to": {"startLines": "73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,255", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4897,5008,5123,5233,5315,5421,5551,5629,5705,5796,5889,5987,6082,6182,6275,6368,6463,6554,6645,6731,6841,6952,7055,7166,7274,7381,7540,23764", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "5003,5118,5228,5310,5416,5546,5624,5700,5791,5884,5982,6077,6177,6270,6363,6458,6549,6640,6726,6836,6947,7050,7161,7269,7376,7535,7634,23846"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\14cf378553861cbecadb791c6e2341a9\\transformed\\core-1.16.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,782", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "148,250,349,451,555,659,777,878"}, "to": {"startLines": "101,102,103,104,105,106,107,263", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "7724,7822,7924,8023,8125,8229,8333,24328", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "7817,7919,8018,8120,8224,8328,8446,24424"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\886fef481c8ce4a2616ccf22a8cd1328\\transformed\\foundation-release\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,140,229", "endColumns": "84,88,94", "endOffsets": "135,224,319"}, "to": {"startLines": "100,268,269", "startColumns": "4,4,4", "startOffsets": "7639,24867,24956", "endColumns": "84,88,94", "endOffsets": "7719,24951,25046"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\493af54b1b0004569d1870cf1ba0af14\\transformed\\media3-session-1.7.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,137,243,325,429,535,630,730,830,935,1046,1118,1223,1329,1421,1494,1582,1661,1760,1828,1897,1982,2059,2157", "endColumns": "81,105,81,103,105,94,99,99,104,110,71,104,105,91,72,87,78,98,67,68,84,76,97,103", "endOffsets": "132,238,320,424,530,625,725,825,930,1041,1113,1218,1324,1416,1489,1577,1656,1755,1823,1892,1977,2054,2152,2256"}, "to": {"startLines": "112,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,203,204,205,206,207,208", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8808,9077,9183,9265,9369,9475,9570,9670,9770,9875,9986,10058,10163,10269,10361,10434,10522,10601,18315,18383,18452,18537,18614,18712", "endColumns": "81,105,81,103,105,94,99,99,104,110,71,104,105,91,72,87,78,98,67,68,84,76,97,103", "endOffsets": "8885,9178,9260,9364,9470,9565,9665,9765,9870,9981,10053,10158,10264,10356,10429,10517,10596,10695,18378,18447,18532,18609,18707,18811"}}]}]}