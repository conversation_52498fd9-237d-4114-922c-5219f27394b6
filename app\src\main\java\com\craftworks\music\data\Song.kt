package com.craftworks.music.data

import android.os.Bundle
import androidx.compose.runtime.Immutable
import androidx.compose.runtime.mutableStateListOf
import androidx.core.net.toUri
import androidx.media3.common.MediaItem
import androidx.media3.common.MediaMetadata
import kotlinx.serialization.Serializable

var songsList: MutableList<MediaData.Song> = mutableStateListOf()

@Immutable
@Serializable
data class Genre(
    val name: String? = ""
)

@Immutable
@Serializable
data class ReplayGain(
    val trackGain: Float? = 0f,
    //val trackPeak: Float? = 0f,
    //val albumPeak: Float? = 0f
)

fun MediaData.Song.toMediaItem(): MediaItem {
    val mediaMetadata =
        MediaMetadata.Builder()
            .setTitle(<EMAIL>)
            .setArtist(<EMAIL>)
            .setAlbumTitle(<EMAIL>)
            .setArtworkUri(<EMAIL>())
            .setRecordingYear(<EMAIL>) // Recording Year is kept, releaseYear gets set to 'null' by ExoPlayer.
            .setIsBrowsable(false).setIsPlayable(true)
            .setMediaType(MediaMetadata.MEDIA_TYPE_MUSIC)
            .setDurationMs(<EMAIL>(1000).toLong())
            .setGenre(<EMAIL>?.joinToString() { it.name ?: "" })
            .setExtras(Bundle().apply {
                putString("navidromeID", <EMAIL>)
                putInt("duration", <EMAIL>)
                putString("format", <EMAIL>)
                putLong("bitrate", <EMAIL>?.toLong() ?: 0)
                putBoolean("isRadio", <EMAIL> == true)
                if (<EMAIL>?.trackGain != null)
                    putFloat("replayGain", <EMAIL>)
            }).build()

    return MediaItem.Builder()
        .setMediaId(<EMAIL>())
        .setUri(<EMAIL>?.toUri())
        .setMediaMetadata(mediaMetadata)
        .build()
}

fun MediaItem.toSong(): MediaData.Song {
    val mediaMetadata = <EMAIL>
    val extras = mediaMetadata.extras

    return MediaData.Song(
        navidromeID = extras?.getString("navidromeID") ?: "",
        title = mediaMetadata.title.toString(),
        artist = mediaMetadata.artist.toString(),
        album = mediaMetadata.albumTitle.toString(),
        imageUrl = mediaMetadata.artworkUri.toString(),
        year = mediaMetadata.recordingYear ?: 0,
        duration = mediaMetadata.durationMs?.toInt()?.div(1000) ?: 0,
        format = extras?.getString("format") ?: "",
        bitrate = extras?.getLong("bitrate")?.toInt(),
        media = <EMAIL>(),
        replayGain = ReplayGain(
            trackGain = extras?.getFloat("replayGain") ?: 0f
        ),
        isRadio = extras?.getBoolean("isRadio"),
        path = "",
        parent = "",
        dateAdded = "",
        bpm = 0,
        albumId = ""
    )
}