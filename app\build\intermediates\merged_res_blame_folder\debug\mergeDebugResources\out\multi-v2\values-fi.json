{"logs": [{"outputFile": "com.craftworks.music.app-mergeDebugResources-3:/values-fi/values-fi.xml", "map": [{"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\2fbbcb27eaccaf382f754b7d51fec492\\transformed\\mediarouter-1.7.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,181,278,382,488,601,719,827,925,1013,1187,1361,1537,1714,1887,2065,2241,2361,2461,2572,2668,2764,2863,2957,3068,3177,3263,3347,3432,3535,3651,3744,3841,3945,4036", "endColumns": "125,96,103,105,112,117,107,97,87,173,173,175,176,172,177,175,119,99,110,95,95,98,93,110,108,85,83,84,102,115,92,96,103,90,94", "endOffsets": "176,273,377,483,596,714,822,920,1008,1182,1356,1532,1709,1882,2060,2236,2356,2456,2567,2663,2759,2858,2952,3063,3172,3258,3342,3427,3530,3646,3739,3836,3940,4031,4126"}, "to": {"startLines": "71,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6456,13591,13688,13792,13898,14011,14129,14237,14335,14423,14597,14771,14947,15124,15297,15475,15651,15771,15871,15982,16078,16174,16273,16367,16478,16587,16673,16757,16842,16945,17061,17154,17251,17355,17446", "endColumns": "125,96,103,105,112,117,107,97,87,173,173,175,176,172,177,175,119,99,110,95,95,98,93,110,108,85,83,84,102,115,92,96,103,90,94", "endOffsets": "6577,13683,13787,13893,14006,14124,14232,14330,14418,14592,14766,14942,15119,15292,15470,15646,15766,15866,15977,16073,16169,16268,16362,16473,16582,16668,16752,16837,16940,17056,17149,17246,17350,17441,17536"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\b12f5ec7860c79fe319ea01d980e4aac\\transformed\\ui-release\\res\\values-fi\\values-fi.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "193,287,372,471,572,661,738,831,922,1004,1085,1167,1239,1326,1402,1482,1556,1633,1705", "endColumns": "93,84,98,100,88,76,92,90,81,80,81,71,86,75,79,73,76,71,121", "endOffsets": "282,367,466,567,656,733,826,917,999,1080,1162,1234,1321,1397,1477,1551,1628,1700,1822"}, "to": {"startLines": "37,38,40,42,43,72,73,172,173,176,177,179,180,181,182,183,186,187,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3596,3690,3845,4021,4122,6582,6659,17541,17632,17927,18008,18171,18243,18330,18406,18486,18830,18907,18979", "endColumns": "93,84,98,100,88,76,92,90,81,80,81,71,86,75,79,73,76,71,121", "endOffsets": "3685,3770,3939,4117,4206,6654,6747,17627,17709,18003,18085,18238,18325,18401,18481,18555,18902,18974,19096"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\c3709e4bfd5d4e5c1ed8a624d490cd12\\transformed\\material-release\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "90", "endOffsets": "141"}, "to": {"startLines": "131", "startColumns": "4", "startOffsets": "13013", "endColumns": "90", "endOffsets": "13099"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\493af54b1b0004569d1870cf1ba0af14\\transformed\\media3-session-1.7.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,242,317,427,516,607,682,771,860,942,1010,1109,1189,1283,1357,1452,1533,1628,1699,1767,1851,1937,2024", "endColumns": "76,109,74,109,88,90,74,88,88,81,67,98,79,93,73,94,80,94,70,67,83,85,86,90", "endOffsets": "127,237,312,422,511,602,677,766,855,937,1005,1104,1184,1278,1352,1447,1528,1623,1694,1762,1846,1932,2019,2110"}, "to": {"startLines": "41,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,132,133,134,135,136,137", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3944,4211,4321,4396,4506,4595,4686,4761,4850,4939,5021,5089,5188,5268,5362,5436,5531,5612,13104,13175,13243,13327,13413,13500", "endColumns": "76,109,74,109,88,90,74,88,88,81,67,98,79,93,73,94,80,94,70,67,83,85,86,90", "endOffsets": "4016,4316,4391,4501,4590,4681,4756,4845,4934,5016,5084,5183,5263,5357,5431,5526,5607,5702,13170,13238,13322,13408,13495,13586"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\64a01e76fd68fe16459fb0e17d50396b\\transformed\\appcompat-1.6.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,422,508,613,731,817,896,987,1080,1175,1269,1363,1456,1552,1651,1742,1836,1916,2023,2124,2221,2327,2427,2525,2675,2775", "endColumns": "107,99,108,85,104,117,85,78,90,92,94,93,93,92,95,98,90,93,79,106,100,96,105,99,97,149,99,80", "endOffsets": "208,308,417,503,608,726,812,891,982,1075,1170,1264,1358,1451,1547,1646,1737,1831,1911,2018,2119,2216,2322,2422,2520,2670,2770,2851"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,178", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,422,508,613,731,817,896,987,1080,1175,1269,1363,1456,1552,1651,1742,1836,1916,2023,2124,2221,2327,2427,2525,2675,18090", "endColumns": "107,99,108,85,104,117,85,78,90,92,94,93,93,92,95,98,90,93,79,106,100,96,105,99,97,149,99,80", "endOffsets": "208,308,417,503,608,726,812,891,982,1075,1170,1264,1358,1451,1547,1646,1737,1831,1911,2018,2119,2216,2322,2422,2520,2670,2770,18166"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\886fef481c8ce4a2616ccf22a8cd1328\\transformed\\foundation-release\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,142,232", "endColumns": "86,89,89", "endOffsets": "137,227,317"}, "to": {"startLines": "29,189,190", "startColumns": "4,4,4", "startOffsets": "2775,19101,19191", "endColumns": "86,89,89", "endOffsets": "2857,19186,19276"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\14cf378553861cbecadb791c6e2341a9\\transformed\\core-1.16.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,351,456,561,673,789", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "146,248,346,451,556,668,784,885"}, "to": {"startLines": "30,31,32,33,34,35,36,184", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2862,2958,3060,3158,3263,3368,3480,18560", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "2953,3055,3153,3258,3363,3475,3591,18656"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\114a5d5301d7649ab21683cbabeecc89\\transformed\\media3-exoplayer-1.7.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,179,241,308,385,455,549,641", "endColumns": "64,58,61,66,76,69,93,91,70", "endOffsets": "115,174,236,303,380,450,544,636,707"}, "to": {"startLines": "61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "5707,5772,5831,5893,5960,6037,6107,6201,6293", "endColumns": "64,58,61,66,76,69,93,91,70", "endOffsets": "5767,5826,5888,5955,6032,6102,6196,6288,6359"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\4f725fd807947c49350c5a696b32f0e9\\transformed\\preference-1.2.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,267,345,480,649,739", "endColumns": "69,91,77,134,168,89,81", "endOffsets": "170,262,340,475,644,734,816"}, "to": {"startLines": "39,70,174,175,185,191,192", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3775,6364,17714,17792,18661,19281,19371", "endColumns": "69,91,77,134,168,89,81", "endOffsets": "3840,6451,17787,17922,18825,19366,19448"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\c9938815eb16e1bbe389a6512658438a\\transformed\\material3-release\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,284,393,507,604,705,823,960,1082,1234,1324,1420,1518,1620,1738,1861,1962,2094,2226,2355,2522,2644,2768,2895,3017,3116,3215,3336,3457,3560,3671,3779,3918,4062,4170,4276,4359,4457,4554,4667,4751,4836,4936,5016,5101,5198,5301,5398,5503,5593,5701,5804,5914,6032,6112,6217", "endColumns": "115,112,108,113,96,100,117,136,121,151,89,95,97,101,117,122,100,131,131,128,166,121,123,126,121,98,98,120,120,102,110,107,138,143,107,105,82,97,96,112,83,84,99,79,84,96,102,96,104,89,107,102,109,117,79,104,98", "endOffsets": "166,279,388,502,599,700,818,955,1077,1229,1319,1415,1513,1615,1733,1856,1957,2089,2221,2350,2517,2639,2763,2890,3012,3111,3210,3331,3452,3555,3666,3774,3913,4057,4165,4271,4354,4452,4549,4662,4746,4831,4931,5011,5096,5193,5296,5393,5498,5588,5696,5799,5909,6027,6107,6212,6311"}, "to": {"startLines": "74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6752,6868,6981,7090,7204,7301,7402,7520,7657,7779,7931,8021,8117,8215,8317,8435,8558,8659,8791,8923,9052,9219,9341,9465,9592,9714,9813,9912,10033,10154,10257,10368,10476,10615,10759,10867,10973,11056,11154,11251,11364,11448,11533,11633,11713,11798,11895,11998,12095,12200,12290,12398,12501,12611,12729,12809,12914", "endColumns": "115,112,108,113,96,100,117,136,121,151,89,95,97,101,117,122,100,131,131,128,166,121,123,126,121,98,98,120,120,102,110,107,138,143,107,105,82,97,96,112,83,84,99,79,84,96,102,96,104,89,107,102,109,117,79,104,98", "endOffsets": "6863,6976,7085,7199,7296,7397,7515,7652,7774,7926,8016,8112,8210,8312,8430,8553,8654,8786,8918,9047,9214,9336,9460,9587,9709,9808,9907,10028,10149,10252,10363,10471,10610,10754,10862,10968,11051,11149,11246,11359,11443,11528,11628,11708,11793,11890,11993,12090,12195,12285,12393,12496,12606,12724,12804,12909,13008"}}]}]}