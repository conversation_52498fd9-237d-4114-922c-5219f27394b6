[versions]
activityCompose = "1.10.1"
composeBom = "2025.05.00"
coreKtx = "1.16.0"
espressoCore = "3.6.1"
githubComposefadingedges = "1.0.4"
junit = "4.13.2"
junitVersion = "1.2.1"
konsumeXml = "1.1"
kotlin = "2.1.0"
datastoreCoreAndroid = "1.1.6"
kotlinxSerializationJson = "1.7.3"
lifecycleRuntimeKtx = "2.9.0"
material3Android = "1.3.2"
media = "1.7.0"
media3Exoplayer = "1.7.1"
media3Session = "1.7.1"
media3Ui = "1.7.1"
mediarouter = "1.7.0"
navigationCompose = "2.9.0"
paletteKtx = "1.0.0"
preferenceKtx = "1.2.1"

[libraries]
androidx-activity-compose = { module = "androidx.activity:activity-compose", version.ref = "activityCompose" }
androidx-compose-bom = { module = "androidx.compose:compose-bom", version.ref = "composeBom" }
androidx-core-ktx = { module = "androidx.core:core-ktx", version.ref = "coreKtx" }
androidx-datastore-preferences = { module = "androidx.datastore:datastore-preferences", version.ref = "datastoreCoreAndroid" }
androidx-espresso-core = { module = "androidx.test.espresso:espresso-core", version.ref = "espressoCore" }
androidx-junit = { module = "androidx.test.ext:junit", version.ref = "junitVersion" }
androidx-lifecycle-runtime-ktx = { module = "androidx.lifecycle:lifecycle-runtime-ktx", version.ref = "lifecycleRuntimeKtx" }
androidx-material3 = { module = "androidx.compose.material3:material3" }
androidx-material3-android = { module = "androidx.compose.material3:material3-android", version.ref = "material3Android" }
androidx-media = { module = "androidx.media:media", version.ref = "media" }
androidx-media3-exoplayer = { module = "androidx.media3:media3-exoplayer", version.ref = "media3Exoplayer" }
androidx-media3-session = { module = "androidx.media3:media3-session", version.ref = "media3Session" }
androidx-media3-ui = { module = "androidx.media3:media3-ui-compose", version.ref = "media3Ui" }
androidx-mediarouter = { module = "androidx.mediarouter:mediarouter", version.ref = "mediarouter" }
androidx-navigation-compose = { module = "androidx.navigation:navigation-compose", version.ref = "navigationCompose" }
androidx-palette-ktx = { module = "androidx.palette:palette-ktx", version.ref = "paletteKtx" }
androidx-preference-ktx = { module = "androidx.preference:preference-ktx", version.ref = "preferenceKtx" }
androidx-ui = { module = "androidx.compose.ui:ui" }
androidx-ui-graphics = { module = "androidx.compose.ui:ui-graphics" }
androidx-ui-test-junit4 = { module = "androidx.compose.ui:ui-test-junit4", version = "1.8.3" }
androidx-ui-test-manifest = { module = "androidx.compose.ui:ui-test-manifest" }
androidx-ui-tooling = { module = "androidx.compose.ui:ui-tooling" }
androidx-ui-tooling-preview = { module = "androidx.compose.ui:ui-tooling-preview" }
coil-compose = { module = "io.coil-kt:coil-compose", version = "2.7.0" }
composefadingedges = { module = "com.github.GIGAMOLE:ComposeFadingEdges", version.ref = "githubComposefadingedges" }
junit = { module = "junit:junit", version.ref = "junit" }
konsume-xml = { module = "com.gitlab.mvysny.konsume-xml:konsume-xml", version.ref = "konsumeXml" }
kotlinx-serialization-json = { module = "org.jetbrains.kotlinx:kotlinx-serialization-json", version.ref = "kotlinxSerializationJson" }
reorderable = { module = "sh.calvin.reorderable:reorderable", version = "2.4.3" }

[plugins]
compose-compiler = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }