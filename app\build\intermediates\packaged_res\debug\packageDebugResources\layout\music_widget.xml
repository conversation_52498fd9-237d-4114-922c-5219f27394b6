<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- Album Art Background -->
    <ImageView
        android:id="@+id/widget_album_art"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        android:src="@drawable/ic_music_note" />

    <!-- Gradient Overlay -->
    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/widget_gradient_overlay" />

    <!-- Content Container -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal"
        android:padding="12dp"
        android:gravity="center_vertical">

        <!-- Song Info -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:layout_marginEnd="8dp">

            <TextView
                android:id="@+id/widget_song_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="No song playing"
                android:textColor="#FFFFFF"
                android:textSize="14sp"
                android:textStyle="bold"
                android:maxLines="1"
                android:ellipsize="end"
                android:shadowColor="#80000000"
                android:shadowDx="1"
                android:shadowDy="1"
                android:shadowRadius="2" />

            <TextView
                android:id="@+id/widget_artist_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Unknown Artist"
                android:textColor="#E0FFFFFF"
                android:textSize="12sp"
                android:maxLines="1"
                android:ellipsize="end"
                android:layout_marginTop="2dp"
                android:shadowColor="#80000000"
                android:shadowDx="1"
                android:shadowDy="1"
                android:shadowRadius="2" />

        </LinearLayout>

        <!-- Control Buttons -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:background="@drawable/widget_controls_background"
            android:padding="6dp">

            <ImageButton
                android:id="@+id/widget_previous"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:background="@drawable/widget_transparent_button"
                android:src="@drawable/ic_skip_previous"
                android:contentDescription="Previous"
                android:scaleType="centerInside"
                android:layout_marginEnd="4dp"
                android:tint="#FFFFFF" />

            <ImageButton
                android:id="@+id/widget_play_pause"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:background="@drawable/widget_transparent_button"
                android:src="@drawable/ic_play_arrow"
                android:contentDescription="Play/Pause"
                android:scaleType="centerInside"
                android:layout_marginHorizontal="4dp"
                android:tint="#FFFFFF" />

            <ImageButton
                android:id="@+id/widget_next"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:background="@drawable/widget_transparent_button"
                android:src="@drawable/ic_skip_next"
                android:contentDescription="Next"
                android:scaleType="centerInside"
                android:layout_marginStart="4dp"
                android:tint="#FFFFFF" />

        </LinearLayout>

    </LinearLayout>

</FrameLayout>
