package com.craftworks.music.ui.viewmodels

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.media3.common.MediaItem
import com.craftworks.music.providers.getAlbums
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

class HomeScreenViewModel : ViewModel(), ReloadableViewModel {
    private val _recentlyPlayedAlbums = MutableStateFlow<List<MediaItem>>(emptyList())
    val recentlyPlayedAlbums: StateFlow<List<MediaItem>> = _recentlyPlayedAlbums.asStateFlow()

    private val _recentAlbums = MutableStateFlow<List<MediaItem>>(emptyList())
    val recentAlbums: StateFlow<List<MediaItem>> = _recentAlbums.asStateFlow()

    private val _mostPlayedAlbums = MutableStateFlow<List<MediaItem>>(emptyList())
    val mostPlayedAlbums: StateFlow<List<MediaItem>> = _mostPlayedAlbums.asStateFlow()

    private val _shuffledAlbums = MutableStateFlow<List<MediaItem>>(emptyList())
    val shuffledAlbums: StateFlow<List<MediaItem>> = _shuffledAlbums.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private var hasLoadedData = false

    fun loadDataIfNeeded() {
        if (!hasLoadedData && !_isLoading.value) {
            reloadData()
        }
    }

    override fun reloadData() {
        if (_isLoading.value) return

        viewModelScope.launch {
            _isLoading.value = true
            try {
                // Load data progressively for better perceived performance
                Log.d("HOME_VM", "Starting progressive data loading...")

                // Load recent albums first (most important)
                _recentAlbums.value = getAlbums("newest", 20, 0, true)
                Log.d("HOME_VM", "Recent albums loaded")

                // Load other data in background
                coroutineScope {
                    val recentlyPlayedDeferred = async {
                        try {
                            getAlbums("recent", 20, 0, true)
                        } catch (e: Exception) {
                            Log.w("HOME_VM", "Failed to load recently played: ${e.message}")
                            emptyList()
                        }
                    }
                    val mostPlayedDeferred = async {
                        try {
                            getAlbums("frequent", 20, 0, true)
                        } catch (e: Exception) {
                            Log.w("HOME_VM", "Failed to load most played: ${e.message}")
                            emptyList()
                        }
                    }
                    val shuffledDeferred = async {
                        try {
                            getAlbums("random", 20, 0, true)
                        } catch (e: Exception) {
                            Log.w("HOME_VM", "Failed to load shuffled: ${e.message}")
                            emptyList()
                        }
                    }

                    _recentlyPlayedAlbums.value = recentlyPlayedDeferred.await()
                    _mostPlayedAlbums.value = mostPlayedDeferred.await()
                    _shuffledAlbums.value = shuffledDeferred.await()
                }
                hasLoadedData = true
                Log.d("HOME_VM", "All home screen data loaded")
            } catch (e: Exception) {
                Log.e("HOME_VM", "Error loading home screen data: ${e.message}", e)
            } finally {
                _isLoading.value = false
            }
        }
    }
}