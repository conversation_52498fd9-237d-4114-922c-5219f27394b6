{"logs": [{"outputFile": "com.craftworks.music.app-mergeDebugResources-3:/values-es-rUS/values-es-rUS.xml", "map": [{"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\14cf378553861cbecadb791c6e2341a9\\transformed\\core-1.16.0\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "30,31,32,33,34,35,36,184", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2901,3000,3102,3202,3300,3407,3513,19010", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "2995,3097,3197,3295,3402,3508,3628,19106"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\b12f5ec7860c79fe319ea01d980e4aac\\transformed\\ui-release\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "186,285,367,465,568,657,736,832,924,1011,1098,1188,1265,1350,1426,1506,1582,1660,1730", "endColumns": "98,81,97,102,88,78,95,91,86,86,89,76,84,75,79,75,77,69,122", "endOffsets": "280,362,460,563,652,731,827,919,1006,1093,1183,1260,1345,1421,1501,1577,1655,1725,1848"}, "to": {"startLines": "37,38,40,42,43,72,73,172,173,176,177,179,180,181,182,183,186,187,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3633,3732,3884,4057,4160,6770,6849,17957,18049,18356,18443,18616,18693,18778,18854,18934,19280,19358,19428", "endColumns": "98,81,97,102,88,78,95,91,86,86,89,76,84,75,79,75,77,69,122", "endOffsets": "3727,3809,3977,4155,4244,6844,6940,18044,18131,18438,18528,18688,18773,18849,18929,19005,19353,19423,19546"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\886fef481c8ce4a2616ccf22a8cd1328\\transformed\\foundation-release\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,135,235", "endColumns": "79,99,101", "endOffsets": "130,230,332"}, "to": {"startLines": "29,189,190", "startColumns": "4,4,4", "startOffsets": "2821,19551,19651", "endColumns": "79,99,101", "endOffsets": "2896,19646,19748"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\64a01e76fd68fe16459fb0e17d50396b\\transformed\\appcompat-1.6.1\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,334,442,527,629,745,830,910,1001,1094,1189,1283,1382,1475,1574,1670,1761,1852,1934,2041,2140,2239,2347,2455,2562,2721,2821", "endColumns": "119,108,107,84,101,115,84,79,90,92,94,93,98,92,98,95,90,90,81,106,98,98,107,107,106,158,99,82", "endOffsets": "220,329,437,522,624,740,825,905,996,1089,1184,1278,1377,1470,1569,1665,1756,1847,1929,2036,2135,2234,2342,2450,2557,2716,2816,2899"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,178", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,334,442,527,629,745,830,910,1001,1094,1189,1283,1382,1475,1574,1670,1761,1852,1934,2041,2140,2239,2347,2455,2562,2721,18533", "endColumns": "119,108,107,84,101,115,84,79,90,92,94,93,98,92,98,95,90,90,81,106,98,98,107,107,106,158,99,82", "endOffsets": "220,329,437,522,624,740,825,905,996,1089,1184,1278,1377,1470,1569,1665,1756,1847,1929,2036,2135,2234,2342,2450,2557,2716,2816,18611"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\4f725fd807947c49350c5a696b32f0e9\\transformed\\preference-1.2.1\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,272,350,492,661,749", "endColumns": "69,96,77,141,168,87,81", "endOffsets": "170,267,345,487,656,744,826"}, "to": {"startLines": "39,70,174,175,185,191,192", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3814,6540,18136,18214,19111,19753,19841", "endColumns": "69,96,77,141,168,87,81", "endOffsets": "3879,6632,18209,18351,19275,19836,19918"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\c3709e4bfd5d4e5c1ed8a624d490cd12\\transformed\\material-release\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "88", "endOffsets": "139"}, "to": {"startLines": "131", "startColumns": "4", "startOffsets": "13306", "endColumns": "88", "endOffsets": "13390"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\114a5d5301d7649ab21683cbabeecc89\\transformed\\media3-exoplayer-1.7.1\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,193,258,327,404,478,567,655", "endColumns": "74,62,64,68,76,73,88,87,68", "endOffsets": "125,188,253,322,399,473,562,650,719"}, "to": {"startLines": "61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "5871,5946,6009,6074,6143,6220,6294,6383,6471", "endColumns": "74,62,64,68,76,73,88,87,68", "endOffsets": "5941,6004,6069,6138,6215,6289,6378,6466,6535"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\c9938815eb16e1bbe389a6512658438a\\transformed\\material3-release\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,177,299,422,542,642,740,855,998,1116,1268,1353,1455,1552,1654,1772,1895,2002,2138,2271,2410,2592,2723,2843,2965,3092,3190,3286,3407,3540,3641,3746,3861,3996,4137,4248,4353,4430,4526,4621,4742,4829,4918,5029,5109,5193,5294,5400,5500,5599,5687,5802,5903,6007,6130,6210,6317", "endColumns": "121,121,122,119,99,97,114,142,117,151,84,101,96,101,117,122,106,135,132,138,181,130,119,121,126,97,95,120,132,100,104,114,134,140,110,104,76,95,94,120,86,88,110,79,83,100,105,99,98,87,114,100,103,122,79,106,98", "endOffsets": "172,294,417,537,637,735,850,993,1111,1263,1348,1450,1547,1649,1767,1890,1997,2133,2266,2405,2587,2718,2838,2960,3087,3185,3281,3402,3535,3636,3741,3856,3991,4132,4243,4348,4425,4521,4616,4737,4824,4913,5024,5104,5188,5289,5395,5495,5594,5682,5797,5898,6002,6125,6205,6312,6411"}, "to": {"startLines": "74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6945,7067,7189,7312,7432,7532,7630,7745,7888,8006,8158,8243,8345,8442,8544,8662,8785,8892,9028,9161,9300,9482,9613,9733,9855,9982,10080,10176,10297,10430,10531,10636,10751,10886,11027,11138,11243,11320,11416,11511,11632,11719,11808,11919,11999,12083,12184,12290,12390,12489,12577,12692,12793,12897,13020,13100,13207", "endColumns": "121,121,122,119,99,97,114,142,117,151,84,101,96,101,117,122,106,135,132,138,181,130,119,121,126,97,95,120,132,100,104,114,134,140,110,104,76,95,94,120,86,88,110,79,83,100,105,99,98,87,114,100,103,122,79,106,98", "endOffsets": "7062,7184,7307,7427,7527,7625,7740,7883,8001,8153,8238,8340,8437,8539,8657,8780,8887,9023,9156,9295,9477,9608,9728,9850,9977,10075,10171,10292,10425,10526,10631,10746,10881,11022,11133,11238,11315,11411,11506,11627,11714,11803,11914,11994,12078,12179,12285,12385,12484,12572,12687,12788,12892,13015,13095,13202,13301"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\2fbbcb27eaccaf382f754b7d51fec492\\transformed\\mediarouter-1.7.0\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,188,287,392,499,610,733,845,946,1034,1214,1399,1585,1770,1947,2138,2321,2446,2545,2655,2752,2848,2943,3037,3153,3281,3364,3452,3537,3641,3758,3851,3959,4070,4157", "endColumns": "132,98,104,106,110,122,111,100,87,179,184,185,184,176,190,182,124,98,109,96,95,94,93,115,127,82,87,84,103,116,92,107,110,86,98", "endOffsets": "183,282,387,494,605,728,840,941,1029,1209,1394,1580,1765,1942,2133,2316,2441,2540,2650,2747,2843,2938,3032,3148,3276,3359,3447,3532,3636,3753,3846,3954,4065,4152,4251"}, "to": {"startLines": "71,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6637,13889,13988,14093,14200,14311,14434,14546,14647,14735,14915,15100,15286,15471,15648,15839,16022,16147,16246,16356,16453,16549,16644,16738,16854,16982,17065,17153,17238,17342,17459,17552,17660,17771,17858", "endColumns": "132,98,104,106,110,122,111,100,87,179,184,185,184,176,190,182,124,98,109,96,95,94,93,115,127,82,87,84,103,116,92,107,110,86,98", "endOffsets": "6765,13983,14088,14195,14306,14429,14541,14642,14730,14910,15095,15281,15466,15643,15834,16017,16142,16241,16351,16448,16544,16639,16733,16849,16977,17060,17148,17233,17337,17454,17547,17655,17766,17853,17952"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\493af54b1b0004569d1870cf1ba0af14\\transformed\\media3-session-1.7.1\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,221,302,424,529,618,708,811,921,1025,1094,1211,1305,1404,1480,1574,1660,1752,1821,1893,1970,2047,2145", "endColumns": "74,90,80,121,104,88,89,102,109,103,68,116,93,98,75,93,85,91,68,71,76,76,97,100", "endOffsets": "125,216,297,419,524,613,703,806,916,1020,1089,1206,1300,1399,1475,1569,1655,1747,1816,1888,1965,2042,2140,2241"}, "to": {"startLines": "41,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,132,133,134,135,136,137", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3982,4249,4340,4421,4543,4648,4737,4827,4930,5040,5144,5213,5330,5424,5523,5599,5693,5779,13395,13464,13536,13613,13690,13788", "endColumns": "74,90,80,121,104,88,89,102,109,103,68,116,93,98,75,93,85,91,68,71,76,76,97,100", "endOffsets": "4052,4335,4416,4538,4643,4732,4822,4925,5035,5139,5208,5325,5419,5518,5594,5688,5774,5866,13459,13531,13608,13685,13783,13884"}}]}]}