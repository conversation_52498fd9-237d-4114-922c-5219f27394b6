R_DEF: Internal format may change without notice
local
color white
color widget_background
color widget_text_primary
color widget_text_secondary
drawable albumplaceholder
drawable baseline_drag_handle_24
drawable chevron_down
drawable favourites
drawable ic_banner_background
drawable ic_banner_foreground
drawable ic_music_note
drawable ic_notification_icon
drawable ic_pause
drawable ic_play_arrow
drawable ic_skip_next
drawable ic_skip_previous
drawable lrclib_logo
drawable lyrics_active
drawable lyrics_inactive
drawable media3_notification_pause
drawable media3_notification_seek_to_next
drawable media3_notification_seek_to_previous
drawable media3_notification_small_icon
drawable placeholder
drawable radioplaceholder
drawable round_favorite_24
drawable round_favorite_border_24
drawable round_music_note_24
drawable round_power_settings_new_24
drawable round_shuffle_28
drawable round_visibility_24
drawable round_visibility_off_24
drawable rounded_add_24
drawable rounded_artist_24
drawable rounded_cell_tower_24
drawable rounded_download_24
drawable rounded_home_24
drawable rounded_library_music_24
drawable rounded_radio
drawable rounded_repeat1_24
drawable rounded_repeat_24
drawable rounded_settings_24
drawable s_a_moreinfo
drawable s_a_navbar_items
drawable s_a_palette
drawable s_a_username
drawable s_m_local_filled
drawable s_m_media_providers
drawable s_m_navidrome
drawable s_m_playback
drawable s_p_scrobble
drawable s_p_transcoding
drawable widget_album_background
drawable widget_background
drawable widget_button_background
drawable widget_controls_background
drawable widget_gradient_overlay
drawable widget_info_background
drawable widget_transparent_button
id widget_album_art
id widget_album_art_2x2
id widget_artist_name
id widget_artist_name_2x2
id widget_next
id widget_next_2x2
id widget_play_pause
id widget_play_pause_2x2
id widget_previous
id widget_previous_2x2
id widget_song_title
id widget_song_title_2x2
layout music_widget
layout music_widget_2x2
mipmap ic_banner
mipmap ic_launcher
mipmap ic_launcher_background
mipmap ic_launcher_foreground
mipmap ic_launcher_monochrome
string Action_Add
string Action_Back
string Action_Cancel
string Action_Confirm
string Action_CreatePlaylist
string Action_Done
string Action_Download
string Action_Exit
string Action_Go
string Action_Login
string Action_Play
string Action_Remove
string Action_Reset
string Action_Search
string Action_Select
string Action_Shuffle
string Action_Success
string Albums
string Artists
string Background_Anim
string Background_Blur
string Background_Plain
string Dialog_Add_Radio
string Dialog_Add_To_Playlist
string Dialog_Background_Type
string Dialog_Delete_Playlist
string Dialog_LocalMusicDirectories
string Dialog_Media_Source
string Dialog_Modify_Radio
string Dialog_New_Playlist
string Dialog_SelectDirectory
string Dialog_Theme
string Dialog_Transcoding
string Label_AddDirectory
string Label_Allow_Self_Signed_Certs
string Label_CacheCleared
string Label_CacheSize
string Label_Confirm_Delete_Playlist
string Label_CurrentDirectory
string Label_Local_Directory
string Label_MoreSongsFrom
string Label_Navidrome_Password
string Label_Navidrome_URL
string Label_Navidrome_Username
string Label_NoDescription
string Label_NoDirectories
string Label_Playlist_Name
string Label_Radio_Add_To_Navidrome
string Label_Radio_Homepage
string Label_Radio_Name
string Label_Radio_URL
string Label_RemoveDirectory
string Label_SelectFolder
string Label_Sorting
string Navidrome_Error
string Navidrome_Sync
string No_Providers_Splash
string Notification_Download_Desc
string Notification_Download_Failure
string Notification_Download_Name
string Notification_Download_Progress
string Notification_Download_Success
string Option_No_Transcoding
string Screen_Discography
string Screen_Top_Songs
string Setting_AutoCache
string Setting_AutoScan
string Setting_Background
string Setting_CacheSize
string Setting_CacheUsage
string Setting_ClearCache
string Setting_LocalMusicDirectories
string Setting_LyricsAnimationSpeed
string Setting_ManualRescan
string Setting_MoreInfo
string Setting_Navbar_Items
string Setting_NavidromeLogo
string Setting_NowPlayingLyricsBlur
string Setting_ProviderDividers
string Setting_Scrobble_Description
string Setting_Scrobble_Percent
string Setting_Transcoding
string Setting_Transcoding_Data
string Setting_Transcoding_Wifi
string Settings_DownloadDirectory
string Settings_Header_Appearance
string Settings_Header_Media
string Settings_Header_Playback
string Source_Local
string Source_Navidrome
string Theme_Dark
string Theme_Light
string Theme_System
string app_name
string most_played
string playlists
string radios
string random_songs
string recently_added
string recently_played
string settings
string songs
string welcome_text
string widget_2x2_description
string widget_description
style Theme.MusicPlayer
xml _generated_res_locale_config
xml automotive_app_desc
xml backup_rules
xml data_extraction_rules
xml music_widget_2x2_info
xml music_widget_info
