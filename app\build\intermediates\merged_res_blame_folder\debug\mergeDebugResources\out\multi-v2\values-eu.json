{"logs": [{"outputFile": "com.craftworks.music.app-mergeDebugResources-3:/values-eu/values-eu.xml", "map": [{"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\c9938815eb16e1bbe389a6512658438a\\transformed\\material3-release\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,202,349,476,621,750,848,963,1103,1222,1367,1451,1556,1652,1752,1871,1992,2102,2245,2389,2524,2715,2840,2962,3086,3208,3305,3402,3530,3665,3763,3866,3972,4119,4270,4378,4478,4554,4650,4745,4864,4951,5039,5149,5229,5314,5409,5512,5603,5702,5791,5899,5999,6105,6223,6303,6407", "endColumns": "146,146,126,144,128,97,114,139,118,144,83,104,95,99,118,120,109,142,143,134,190,124,121,123,121,96,96,127,134,97,102,105,146,150,107,99,75,95,94,118,86,87,109,79,84,94,102,90,98,88,107,99,105,117,79,103,94", "endOffsets": "197,344,471,616,745,843,958,1098,1217,1362,1446,1551,1647,1747,1866,1987,2097,2240,2384,2519,2710,2835,2957,3081,3203,3300,3397,3525,3660,3758,3861,3967,4114,4265,4373,4473,4549,4645,4740,4859,4946,5034,5144,5224,5309,5404,5507,5598,5697,5786,5894,5994,6100,6218,6298,6402,6497"}, "to": {"startLines": "74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6983,7130,7277,7404,7549,7678,7776,7891,8031,8150,8295,8379,8484,8580,8680,8799,8920,9030,9173,9317,9452,9643,9768,9890,10014,10136,10233,10330,10458,10593,10691,10794,10900,11047,11198,11306,11406,11482,11578,11673,11792,11879,11967,12077,12157,12242,12337,12440,12531,12630,12719,12827,12927,13033,13151,13231,13335", "endColumns": "146,146,126,144,128,97,114,139,118,144,83,104,95,99,118,120,109,142,143,134,190,124,121,123,121,96,96,127,134,97,102,105,146,150,107,99,75,95,94,118,86,87,109,79,84,94,102,90,98,88,107,99,105,117,79,103,94", "endOffsets": "7125,7272,7399,7544,7673,7771,7886,8026,8145,8290,8374,8479,8575,8675,8794,8915,9025,9168,9312,9447,9638,9763,9885,10009,10131,10228,10325,10453,10588,10686,10789,10895,11042,11193,11301,11401,11477,11573,11668,11787,11874,11962,12072,12152,12237,12332,12435,12526,12625,12714,12822,12922,13028,13146,13226,13330,13425"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\114a5d5301d7649ab21683cbabeecc89\\transformed\\media3-exoplayer-1.7.1\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,187,253,325,402,476,587,685", "endColumns": "68,62,65,71,76,73,110,97,67", "endOffsets": "119,182,248,320,397,471,582,680,748"}, "to": {"startLines": "61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "5903,5972,6035,6101,6173,6250,6324,6435,6533", "endColumns": "68,62,65,71,76,73,110,97,67", "endOffsets": "5967,6030,6096,6168,6245,6319,6430,6528,6596"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\64a01e76fd68fe16459fb0e17d50396b\\transformed\\appcompat-1.6.1\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,312,422,508,614,738,824,905,997,1091,1187,1281,1382,1476,1572,1669,1761,1854,1936,2045,2154,2253,2362,2469,2580,2751,2850", "endColumns": "108,97,109,85,105,123,85,80,91,93,95,93,100,93,95,96,91,92,81,108,108,98,108,106,110,170,98,82", "endOffsets": "209,307,417,503,609,733,819,900,992,1086,1182,1276,1377,1471,1567,1664,1756,1849,1931,2040,2149,2248,2357,2464,2575,2746,2845,2928"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,178", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,312,422,508,614,738,824,905,997,1091,1187,1281,1382,1476,1572,1669,1761,1854,1936,2045,2154,2253,2362,2469,2580,2751,18496", "endColumns": "108,97,109,85,105,123,85,80,91,93,95,93,100,93,95,96,91,92,81,108,108,98,108,106,110,170,98,82", "endOffsets": "209,307,417,503,609,733,819,900,992,1086,1182,1276,1377,1471,1567,1664,1756,1849,1931,2040,2149,2248,2357,2464,2575,2746,2845,18574"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\14cf378553861cbecadb791c6e2341a9\\transformed\\core-1.16.0\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,564,667,786", "endColumns": "97,102,99,102,104,102,118,100", "endOffsets": "148,251,351,454,559,662,781,882"}, "to": {"startLines": "30,31,32,33,34,35,36,184", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2936,3034,3137,3237,3340,3445,3548,18975", "endColumns": "97,102,99,102,104,102,118,100", "endOffsets": "3029,3132,3232,3335,3440,3543,3662,19071"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\886fef481c8ce4a2616ccf22a8cd1328\\transformed\\foundation-release\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,141,227", "endColumns": "85,85,88", "endOffsets": "136,222,311"}, "to": {"startLines": "29,189,190", "startColumns": "4,4,4", "startOffsets": "2850,19510,19596", "endColumns": "85,85,88", "endOffsets": "2931,19591,19680"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\2fbbcb27eaccaf382f754b7d51fec492\\transformed\\mediarouter-1.7.0\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,270,370,476,587,705,812,908,995,1184,1350,1522,1693,1862,2035,2205,2315,2413,2518,2613,2708,2804,2897,3008,3132,3217,3308,3393,3494,3606,3700,3802,3917,4004", "endColumns": "119,94,99,105,110,117,106,95,86,188,165,171,170,168,172,169,109,97,104,94,94,95,92,110,123,84,90,84,100,111,93,101,114,86,93", "endOffsets": "170,265,365,471,582,700,807,903,990,1179,1345,1517,1688,1857,2030,2200,2310,2408,2513,2608,2703,2799,2892,3003,3127,3212,3303,3388,3489,3601,3695,3797,3912,3999,4093"}, "to": {"startLines": "71,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6700,14009,14104,14204,14310,14421,14539,14646,14742,14829,15018,15184,15356,15527,15696,15869,16039,16149,16247,16352,16447,16542,16638,16731,16842,16966,17051,17142,17227,17328,17440,17534,17636,17751,17838", "endColumns": "119,94,99,105,110,117,106,95,86,188,165,171,170,168,172,169,109,97,104,94,94,95,92,110,123,84,90,84,100,111,93,101,114,86,93", "endOffsets": "6815,14099,14199,14305,14416,14534,14641,14737,14824,15013,15179,15351,15522,15691,15864,16034,16144,16242,16347,16442,16537,16633,16726,16837,16961,17046,17137,17222,17323,17435,17529,17631,17746,17833,17927"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\b12f5ec7860c79fe319ea01d980e4aac\\transformed\\ui-release\\res\\values-eu\\values-eu.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "192,283,364,465,569,661,737,824,913,997,1085,1175,1249,1334,1411,1493,1571,1648,1716", "endColumns": "90,80,100,103,91,75,86,88,83,87,89,73,84,76,81,77,76,67,119", "endOffsets": "278,359,460,564,656,732,819,908,992,1080,1170,1244,1329,1406,1488,1566,1643,1711,1831"}, "to": {"startLines": "37,38,40,42,43,72,73,172,173,176,177,179,180,181,182,183,186,187,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3667,3758,3910,4095,4199,6820,6896,17932,18021,18318,18406,18579,18653,18738,18815,18897,19245,19322,19390", "endColumns": "90,80,100,103,91,75,86,88,83,87,89,73,84,76,81,77,76,67,119", "endOffsets": "3753,3834,4006,4194,4286,6891,6978,18016,18100,18401,18491,18648,18733,18810,18892,18970,19317,19385,19505"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\4f725fd807947c49350c5a696b32f0e9\\transformed\\preference-1.2.1\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,275,354,488,657,747", "endColumns": "70,98,78,133,168,89,83", "endOffsets": "171,270,349,483,652,742,826"}, "to": {"startLines": "39,70,174,175,185,191,192", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3839,6601,18105,18184,19076,19685,19775", "endColumns": "70,98,78,133,168,89,83", "endOffsets": "3905,6695,18179,18313,19240,19770,19854"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\493af54b1b0004569d1870cf1ba0af14\\transformed\\media3-session-1.7.1\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,139,239,325,433,543,638,722,811,913,1004,1096,1207,1302,1399,1477,1574,1657,1751,1821,1896,1971,2050,2144", "endColumns": "83,99,85,107,109,94,83,88,101,90,91,110,94,96,77,96,82,93,69,74,74,78,93,96", "endOffsets": "134,234,320,428,538,633,717,806,908,999,1091,1202,1297,1394,1472,1569,1652,1746,1816,1891,1966,2045,2139,2236"}, "to": {"startLines": "41,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,132,133,134,135,136,137", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4011,4291,4391,4477,4585,4695,4790,4874,4963,5065,5156,5248,5359,5454,5551,5629,5726,5809,13519,13589,13664,13739,13818,13912", "endColumns": "83,99,85,107,109,94,83,88,101,90,91,110,94,96,77,96,82,93,69,74,74,78,93,96", "endOffsets": "4090,4386,4472,4580,4690,4785,4869,4958,5060,5151,5243,5354,5449,5546,5624,5721,5804,5898,13584,13659,13734,13813,13907,14004"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\c3709e4bfd5d4e5c1ed8a624d490cd12\\transformed\\material-release\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "88", "endOffsets": "139"}, "to": {"startLines": "131", "startColumns": "4", "startOffsets": "13430", "endColumns": "88", "endOffsets": "13514"}}]}]}