{"logs": [{"outputFile": "com.craftworks.music.app-mergeDebugResources-3:/values-ca/values-ca.xml", "map": [{"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\2fbbcb27eaccaf382f754b7d51fec492\\transformed\\mediarouter-1.7.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,196,289,389,499,605,732,853,961,1053,1232,1426,1624,1824,2021,2224,2423,2551,2653,2768,2864,2959,3054,3148,3268,3400,3491,3579,3662,3763,3875,3967,4074,4184,4271", "endColumns": "140,92,99,109,105,126,120,107,91,178,193,197,199,196,202,198,127,101,114,95,94,94,93,119,131,90,87,82,100,111,91,106,109,86,97", "endOffsets": "191,284,384,494,600,727,848,956,1048,1227,1421,1619,1819,2016,2219,2418,2546,2648,2763,2859,2954,3049,3143,3263,3395,3486,3574,3657,3758,3870,3962,4069,4179,4266,4364"}, "to": {"startLines": "71,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6711,14014,14107,14207,14317,14423,14550,14671,14779,14871,15050,15244,15442,15642,15839,16042,16241,16369,16471,16586,16682,16777,16872,16966,17086,17218,17309,17397,17480,17581,17693,17785,17892,18002,18089", "endColumns": "140,92,99,109,105,126,120,107,91,178,193,197,199,196,202,198,127,101,114,95,94,94,93,119,131,90,87,82,100,111,91,106,109,86,97", "endOffsets": "6847,14102,14202,14312,14418,14545,14666,14774,14866,15045,15239,15437,15637,15834,16037,16236,16364,16466,16581,16677,16772,16867,16961,17081,17213,17304,17392,17475,17576,17688,17780,17887,17997,18084,18182"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\64a01e76fd68fe16459fb0e17d50396b\\transformed\\appcompat-1.6.1\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,228,333,440,523,629,755,839,918,1009,1102,1195,1290,1388,1481,1574,1668,1759,1850,1931,2042,2150,2248,2358,2463,2571,2731,2830", "endColumns": "122,104,106,82,105,125,83,78,90,92,92,94,97,92,92,93,90,90,80,110,107,97,109,104,107,159,98,81", "endOffsets": "223,328,435,518,624,750,834,913,1004,1097,1190,1285,1383,1476,1569,1663,1754,1845,1926,2037,2145,2243,2353,2458,2566,2726,2825,2907"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,178", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,228,333,440,523,629,755,839,918,1009,1102,1195,1290,1388,1481,1574,1668,1759,1850,1931,2042,2150,2248,2358,2463,2571,2731,18761", "endColumns": "122,104,106,82,105,125,83,78,90,92,92,94,97,92,92,93,90,90,80,110,107,97,109,104,107,159,98,81", "endOffsets": "223,328,435,518,624,750,834,913,1004,1097,1190,1285,1383,1476,1569,1663,1754,1845,1926,2037,2145,2243,2353,2458,2566,2726,2825,18838"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\886fef481c8ce4a2616ccf22a8cd1328\\transformed\\foundation-release\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,144,242", "endColumns": "88,97,101", "endOffsets": "139,237,339"}, "to": {"startLines": "29,189,190", "startColumns": "4,4,4", "startOffsets": "2830,19776,19874", "endColumns": "88,97,101", "endOffsets": "2914,19869,19971"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\14cf378553861cbecadb791c6e2341a9\\transformed\\core-1.16.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,449,555,660,786", "endColumns": "95,101,98,96,105,104,125,100", "endOffsets": "146,248,347,444,550,655,781,882"}, "to": {"startLines": "30,31,32,33,34,35,36,184", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2919,3015,3117,3216,3313,3419,3524,19233", "endColumns": "95,101,98,96,105,104,125,100", "endOffsets": "3010,3112,3211,3308,3414,3519,3645,19329"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\493af54b1b0004569d1870cf1ba0af14\\transformed\\media3-session-1.7.1\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,136,250,339,454,559,660,744,838,937,1040,1115,1230,1332,1430,1504,1598,1679,1771,1847,1919,1997,2073,2167", "endColumns": "80,113,88,114,104,100,83,93,98,102,74,114,101,97,73,93,80,91,75,71,77,75,93,98", "endOffsets": "131,245,334,449,554,655,739,833,932,1035,1110,1225,1327,1425,1499,1593,1674,1766,1842,1914,1992,2068,2162,2261"}, "to": {"startLines": "41,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,132,133,134,135,136,137", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4004,4277,4391,4480,4595,4700,4801,4885,4979,5078,5181,5256,5371,5473,5571,5645,5739,5820,13519,13595,13667,13745,13821,13915", "endColumns": "80,113,88,114,104,100,83,93,98,102,74,114,101,97,73,93,80,91,75,71,77,75,93,98", "endOffsets": "4080,4386,4475,4590,4695,4796,4880,4974,5073,5176,5251,5366,5468,5566,5640,5734,5815,5907,13590,13662,13740,13816,13910,14009"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\c9938815eb16e1bbe389a6512658438a\\transformed\\material3-release\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,176,295,414,534,634,732,847,989,1104,1263,1347,1445,1543,1644,1761,1890,1993,2134,2274,2415,2581,2714,2831,2952,3081,3180,3277,3398,3543,3649,3762,3876,4015,4160,4269,4376,4462,4563,4664,4775,4861,4947,5058,5138,5222,5323,5431,5530,5634,5721,5834,5934,6041,6160,6240,6357", "endColumns": "120,118,118,119,99,97,114,141,114,158,83,97,97,100,116,128,102,140,139,140,165,132,116,120,128,98,96,120,144,105,112,113,138,144,108,106,85,100,100,110,85,85,110,79,83,100,107,98,103,86,112,99,106,118,79,116,106", "endOffsets": "171,290,409,529,629,727,842,984,1099,1258,1342,1440,1538,1639,1756,1885,1988,2129,2269,2410,2576,2709,2826,2947,3076,3175,3272,3393,3538,3644,3757,3871,4010,4155,4264,4371,4457,4558,4659,4770,4856,4942,5053,5133,5217,5318,5426,5525,5629,5716,5829,5929,6036,6155,6235,6352,6459"}, "to": {"startLines": "74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7021,7142,7261,7380,7500,7600,7698,7813,7955,8070,8229,8313,8411,8509,8610,8727,8856,8959,9100,9240,9381,9547,9680,9797,9918,10047,10146,10243,10364,10509,10615,10728,10842,10981,11126,11235,11342,11428,11529,11630,11741,11827,11913,12024,12104,12188,12289,12397,12496,12600,12687,12800,12900,13007,13126,13206,13323", "endColumns": "120,118,118,119,99,97,114,141,114,158,83,97,97,100,116,128,102,140,139,140,165,132,116,120,128,98,96,120,144,105,112,113,138,144,108,106,85,100,100,110,85,85,110,79,83,100,107,98,103,86,112,99,106,118,79,116,106", "endOffsets": "7137,7256,7375,7495,7595,7693,7808,7950,8065,8224,8308,8406,8504,8605,8722,8851,8954,9095,9235,9376,9542,9675,9792,9913,10042,10141,10238,10359,10504,10610,10723,10837,10976,11121,11230,11337,11423,11524,11625,11736,11822,11908,12019,12099,12183,12284,12392,12491,12595,12682,12795,12895,13002,13121,13201,13318,13425"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\c3709e4bfd5d4e5c1ed8a624d490cd12\\transformed\\material-release\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "88", "endOffsets": "139"}, "to": {"startLines": "131", "startColumns": "4", "startOffsets": "13430", "endColumns": "88", "endOffsets": "13514"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\4f725fd807947c49350c5a696b32f0e9\\transformed\\preference-1.2.1\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,174,270,347,490,659,746", "endColumns": "68,95,76,142,168,86,80", "endOffsets": "169,265,342,485,654,741,822"}, "to": {"startLines": "39,70,174,175,185,191,192", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3831,6615,18364,18441,19334,19976,20063", "endColumns": "68,95,76,142,168,86,80", "endOffsets": "3895,6706,18436,18579,19498,20058,20139"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\b12f5ec7860c79fe319ea01d980e4aac\\transformed\\ui-release\\res\\values-ca\\values-ca.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "195,292,376,480,583,672,750,841,932,1018,1104,1195,1271,1356,1431,1510,1585,1667,1738", "endColumns": "96,83,103,102,88,77,90,90,85,85,90,75,84,74,78,74,81,70,119", "endOffsets": "287,371,475,578,667,745,836,927,1013,1099,1190,1266,1351,1426,1505,1580,1662,1733,1853"}, "to": {"startLines": "37,38,40,42,43,72,73,172,173,176,177,179,180,181,182,183,186,187,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3650,3747,3900,4085,4188,6852,6930,18187,18278,18584,18670,18843,18919,19004,19079,19158,19503,19585,19656", "endColumns": "96,83,103,102,88,77,90,90,85,85,90,75,84,74,78,74,81,70,119", "endOffsets": "3742,3826,3999,4183,4272,6925,7016,18273,18359,18665,18756,18914,18999,19074,19153,19228,19580,19651,19771"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\114a5d5301d7649ab21683cbabeecc89\\transformed\\media3-exoplayer-1.7.1\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,192,261,331,407,483,581,676", "endColumns": "77,58,68,69,75,75,97,94,81", "endOffsets": "128,187,256,326,402,478,576,671,753"}, "to": {"startLines": "61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "5912,5990,6049,6118,6188,6264,6340,6438,6533", "endColumns": "77,58,68,69,75,75,97,94,81", "endOffsets": "5985,6044,6113,6183,6259,6335,6433,6528,6610"}}]}]}