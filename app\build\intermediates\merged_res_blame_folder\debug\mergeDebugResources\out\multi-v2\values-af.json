{"logs": [{"outputFile": "com.craftworks.music.app-mergeDebugResources-3:/values-af/values-af.xml", "map": [{"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\2fbbcb27eaccaf382f754b7d51fec492\\transformed\\mediarouter-1.7.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,177,274,376,482,588,714,828,927,1014,1195,1373,1552,1734,1908,2092,2275,2398,2494,2601,2700,2794,2887,2980,3102,3217,3305,3388,3470,3569,3667,3760,3865,3972,4059", "endColumns": "121,96,101,105,105,125,113,98,86,180,177,178,181,173,183,182,122,95,106,98,93,92,92,121,114,87,82,81,98,97,92,104,106,86,95", "endOffsets": "172,269,371,477,583,709,823,922,1009,1190,1368,1547,1729,1903,2087,2270,2393,2489,2596,2695,2789,2882,2975,3097,3212,3300,3383,3465,3564,3662,3755,3860,3967,4054,4150"}, "to": {"startLines": "71,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6445,13498,13595,13697,13803,13909,14035,14149,14248,14335,14516,14694,14873,15055,15229,15413,15596,15719,15815,15922,16021,16115,16208,16301,16423,16538,16626,16709,16791,16890,16988,17081,17186,17293,17380", "endColumns": "121,96,101,105,105,125,113,98,86,180,177,178,181,173,183,182,122,95,106,98,93,92,92,121,114,87,82,81,98,97,92,104,106,86,95", "endOffsets": "6562,13590,13692,13798,13904,14030,14144,14243,14330,14511,14689,14868,15050,15224,15408,15591,15714,15810,15917,16016,16110,16203,16296,16418,16533,16621,16704,16786,16885,16983,17076,17181,17288,17375,17471"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\b12f5ec7860c79fe319ea01d980e4aac\\transformed\\ui-release\\res\\values-af\\values-af.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "180,276,363,460,561,647,723,814,904,990,1068,1149,1220,1309,1384,1455,1526,1607,1677", "endColumns": "95,86,96,100,85,75,90,89,85,77,80,70,88,74,70,70,80,69,119", "endOffsets": "271,358,455,556,642,718,809,899,985,1063,1144,1215,1304,1379,1450,1521,1602,1672,1792"}, "to": {"startLines": "37,38,40,42,43,72,73,172,173,176,177,179,180,181,182,183,186,187,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3583,3679,3837,4007,4108,6567,6643,17476,17566,17872,17950,18112,18183,18272,18347,18418,18759,18840,18910", "endColumns": "95,86,96,100,85,75,90,89,85,77,80,70,88,74,70,70,80,69,119", "endOffsets": "3674,3761,3929,4103,4189,6638,6729,17561,17647,17945,18026,18178,18267,18342,18413,18484,18835,18905,19025"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\4f725fd807947c49350c5a696b32f0e9\\transformed\\preference-1.2.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,264,345,484,653,732", "endColumns": "70,87,80,138,168,78,76", "endOffsets": "171,259,340,479,648,727,804"}, "to": {"startLines": "39,70,174,175,185,191,192", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3766,6357,17652,17733,18590,19202,19281", "endColumns": "70,87,80,138,168,78,76", "endOffsets": "3832,6440,17728,17867,18754,19276,19353"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\c9938815eb16e1bbe389a6512658438a\\transformed\\material3-release\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,290,405,521,621,725,846,987,1115,1257,1342,1441,1531,1627,1742,1863,1967,2095,2220,2352,2518,2643,2765,2888,3017,3108,3207,3323,3449,3549,3659,3762,3899,4039,4145,4243,4320,4414,4508,4612,4697,4785,4890,4971,5054,5153,5251,5346,5444,5532,5635,5735,5838,5954,6035,6135", "endColumns": "116,117,114,115,99,103,120,140,127,141,84,98,89,95,114,120,103,127,124,131,165,124,121,122,128,90,98,115,125,99,109,102,136,139,105,97,76,93,93,103,84,87,104,80,82,98,97,94,97,87,102,99,102,115,80,99,95", "endOffsets": "167,285,400,516,616,720,841,982,1110,1252,1337,1436,1526,1622,1737,1858,1962,2090,2215,2347,2513,2638,2760,2883,3012,3103,3202,3318,3444,3544,3654,3757,3894,4034,4140,4238,4315,4409,4503,4607,4692,4780,4885,4966,5049,5148,5246,5341,5439,5527,5630,5730,5833,5949,6030,6130,6226"}, "to": {"startLines": "74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6734,6851,6969,7084,7200,7300,7404,7525,7666,7794,7936,8021,8120,8210,8306,8421,8542,8646,8774,8899,9031,9197,9322,9444,9567,9696,9787,9886,10002,10128,10228,10338,10441,10578,10718,10824,10922,10999,11093,11187,11291,11376,11464,11569,11650,11733,11832,11930,12025,12123,12211,12314,12414,12517,12633,12714,12814", "endColumns": "116,117,114,115,99,103,120,140,127,141,84,98,89,95,114,120,103,127,124,131,165,124,121,122,128,90,98,115,125,99,109,102,136,139,105,97,76,93,93,103,84,87,104,80,82,98,97,94,97,87,102,99,102,115,80,99,95", "endOffsets": "6846,6964,7079,7195,7295,7399,7520,7661,7789,7931,8016,8115,8205,8301,8416,8537,8641,8769,8894,9026,9192,9317,9439,9562,9691,9782,9881,9997,10123,10223,10333,10436,10573,10713,10819,10917,10994,11088,11182,11286,11371,11459,11564,11645,11728,11827,11925,12020,12118,12206,12309,12409,12512,12628,12709,12809,12905"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\64a01e76fd68fe16459fb0e17d50396b\\transformed\\appcompat-1.6.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,309,415,500,603,721,798,874,965,1058,1153,1247,1346,1439,1534,1633,1728,1822,1903,2010,2115,2212,2320,2423,2525,2679,2777", "endColumns": "107,95,105,84,102,117,76,75,90,92,94,93,98,92,94,98,94,93,80,106,104,96,107,102,101,153,97,80", "endOffsets": "208,304,410,495,598,716,793,869,960,1053,1148,1242,1341,1434,1529,1628,1723,1817,1898,2005,2110,2207,2315,2418,2520,2674,2772,2853"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,178", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,309,415,500,603,721,798,874,965,1058,1153,1247,1346,1439,1534,1633,1728,1822,1903,2010,2115,2212,2320,2423,2525,2679,18031", "endColumns": "107,95,105,84,102,117,76,75,90,92,94,93,98,92,94,98,94,93,80,106,104,96,107,102,101,153,97,80", "endOffsets": "208,304,410,495,598,716,793,869,960,1053,1148,1242,1341,1434,1529,1628,1723,1817,1898,2005,2110,2207,2315,2418,2520,2674,2772,18107"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\886fef481c8ce4a2616ccf22a8cd1328\\transformed\\foundation-release\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,129,216", "endColumns": "73,86,84", "endOffsets": "124,211,296"}, "to": {"startLines": "29,189,190", "startColumns": "4,4,4", "startOffsets": "2777,19030,19117", "endColumns": "73,86,84", "endOffsets": "2846,19112,19197"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\114a5d5301d7649ab21683cbabeecc89\\transformed\\media3-exoplayer-1.7.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,184,250,317,392,462,551,635", "endColumns": "68,59,65,66,74,69,88,83,71", "endOffsets": "119,179,245,312,387,457,546,630,702"}, "to": {"startLines": "61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "5705,5774,5834,5900,5967,6042,6112,6201,6285", "endColumns": "68,59,65,66,74,69,88,83,71", "endOffsets": "5769,5829,5895,5962,6037,6107,6196,6280,6352"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\14cf378553861cbecadb791c6e2341a9\\transformed\\core-1.16.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,451,558,667,787", "endColumns": "97,101,97,97,106,108,119,100", "endOffsets": "148,250,348,446,553,662,782,883"}, "to": {"startLines": "30,31,32,33,34,35,36,184", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2851,2949,3051,3149,3247,3354,3463,18489", "endColumns": "97,101,97,97,106,108,119,100", "endOffsets": "2944,3046,3144,3242,3349,3458,3578,18585"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\c3709e4bfd5d4e5c1ed8a624d490cd12\\transformed\\material-release\\res\\values-af\\values-af.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "92", "endOffsets": "143"}, "to": {"startLines": "131", "startColumns": "4", "startOffsets": "12910", "endColumns": "92", "endOffsets": "12998"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\493af54b1b0004569d1870cf1ba0af14\\transformed\\media3-session-1.7.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,228,301,401,494,573,669,749,846,938,1003,1105,1194,1295,1371,1465,1545,1639,1712,1779,1859,1942,2037", "endColumns": "72,99,72,99,92,78,95,79,96,91,64,101,88,100,75,93,79,93,72,66,79,82,94,96", "endOffsets": "123,223,296,396,489,568,664,744,841,933,998,1100,1189,1290,1366,1460,1540,1634,1707,1774,1854,1937,2032,2129"}, "to": {"startLines": "41,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,132,133,134,135,136,137", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3934,4194,4294,4367,4467,4560,4639,4735,4815,4912,5004,5069,5171,5260,5361,5437,5531,5611,13003,13076,13143,13223,13306,13401", "endColumns": "72,99,72,99,92,78,95,79,96,91,64,101,88,100,75,93,79,93,72,66,79,82,94,96", "endOffsets": "4002,4289,4362,4462,4555,4634,4730,4810,4907,4999,5064,5166,5255,5356,5432,5526,5606,5700,13071,13138,13218,13301,13396,13493"}}]}]}