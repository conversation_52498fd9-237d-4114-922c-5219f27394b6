{"logs": [{"outputFile": "com.craftworks.music.app-mergeDebugResources-3:/values-sr/values-sr.xml", "map": [{"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\886fef481c8ce4a2616ccf22a8cd1328\\transformed\\foundation-release\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,143,231", "endColumns": "87,87,89", "endOffsets": "138,226,316"}, "to": {"startLines": "29,189,190", "startColumns": "4,4,4", "startOffsets": "2833,19252,19340", "endColumns": "87,87,89", "endOffsets": "2916,19335,19425"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\14cf378553861cbecadb791c6e2341a9\\transformed\\core-1.16.0\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "30,31,32,33,34,35,36,184", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2921,3019,3121,3218,3322,3426,3531,18715", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "3014,3116,3213,3317,3421,3526,3642,18811"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\4f725fd807947c49350c5a696b32f0e9\\transformed\\preference-1.2.1\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,263,343,495,664,750", "endColumns": "70,86,79,151,168,85,81", "endOffsets": "171,258,338,490,659,745,827"}, "to": {"startLines": "39,70,174,175,185,191,192", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3831,6486,17838,17918,18816,19430,19516", "endColumns": "70,86,79,151,168,85,81", "endOffsets": "3897,6568,17913,18065,18980,19511,19593"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\c9938815eb16e1bbe389a6512658438a\\transformed\\material3-release\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,287,414,530,628,722,833,969,1088,1230,1315,1415,1510,1608,1724,1849,1954,2095,2235,2368,2548,2673,2793,2918,3040,3136,3234,3351,3481,3581,3683,3792,3934,4083,4192,4295,4372,4470,4568,4677,4766,4852,4959,5039,5122,5219,5322,5415,5513,5600,5708,5805,5907,6040,6120,6227", "endColumns": "115,115,126,115,97,93,110,135,118,141,84,99,94,97,115,124,104,140,139,132,179,124,119,124,121,95,97,116,129,99,101,108,141,148,108,102,76,97,97,108,88,85,106,79,82,96,102,92,97,86,107,96,101,132,79,106,96", "endOffsets": "166,282,409,525,623,717,828,964,1083,1225,1310,1410,1505,1603,1719,1844,1949,2090,2230,2363,2543,2668,2788,2913,3035,3131,3229,3346,3476,3576,3678,3787,3929,4078,4187,4290,4367,4465,4563,4672,4761,4847,4954,5034,5117,5214,5317,5410,5508,5595,5703,5800,5902,6035,6115,6222,6319"}, "to": {"startLines": "74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6868,6984,7100,7227,7343,7441,7535,7646,7782,7901,8043,8128,8228,8323,8421,8537,8662,8767,8908,9048,9181,9361,9486,9606,9731,9853,9949,10047,10164,10294,10394,10496,10605,10747,10896,11005,11108,11185,11283,11381,11490,11579,11665,11772,11852,11935,12032,12135,12228,12326,12413,12521,12618,12720,12853,12933,13040", "endColumns": "115,115,126,115,97,93,110,135,118,141,84,99,94,97,115,124,104,140,139,132,179,124,119,124,121,95,97,116,129,99,101,108,141,148,108,102,76,97,97,108,88,85,106,79,82,96,102,92,97,86,107,96,101,132,79,106,96", "endOffsets": "6979,7095,7222,7338,7436,7530,7641,7777,7896,8038,8123,8223,8318,8416,8532,8657,8762,8903,9043,9176,9356,9481,9601,9726,9848,9944,10042,10159,10289,10389,10491,10600,10742,10891,11000,11103,11180,11278,11376,11485,11574,11660,11767,11847,11930,12027,12130,12223,12321,12408,12516,12613,12715,12848,12928,13035,13132"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\2fbbcb27eaccaf382f754b7d51fec492\\transformed\\mediarouter-1.7.0\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,182,280,385,492,607,730,837,933,1021,1199,1373,1547,1721,1887,2062,2232,2354,2448,2554,2652,2745,2841,2934,3050,3162,3248,3331,3417,3522,3631,3724,3824,3929,4015", "endColumns": "126,97,104,106,114,122,106,95,87,177,173,173,173,165,174,169,121,93,105,97,92,95,92,115,111,85,82,85,104,108,92,99,104,85,93", "endOffsets": "177,275,380,487,602,725,832,928,1016,1194,1368,1542,1716,1882,2057,2227,2349,2443,2549,2647,2740,2836,2929,3045,3157,3243,3326,3412,3517,3626,3719,3819,3924,4010,4104"}, "to": {"startLines": "71,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6573,13734,13832,13937,14044,14159,14282,14389,14485,14573,14751,14925,15099,15273,15439,15614,15784,15906,16000,16106,16204,16297,16393,16486,16602,16714,16800,16883,16969,17074,17183,17276,17376,17481,17567", "endColumns": "126,97,104,106,114,122,106,95,87,177,173,173,173,165,174,169,121,93,105,97,92,95,92,115,111,85,82,85,104,108,92,99,104,85,93", "endOffsets": "6695,13827,13932,14039,14154,14277,14384,14480,14568,14746,14920,15094,15268,15434,15609,15779,15901,15995,16101,16199,16292,16388,16481,16597,16709,16795,16878,16964,17069,17178,17271,17371,17476,17562,17656"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\64a01e76fd68fe16459fb0e17d50396b\\transformed\\appcompat-1.6.1\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,815,896,987,1080,1175,1269,1369,1462,1557,1662,1753,1844,1930,2035,2141,2244,2350,2459,2566,2736,2833", "endColumns": "106,100,105,85,103,121,83,80,90,92,94,93,99,92,94,104,90,90,85,104,105,102,105,108,106,169,96,86", "endOffsets": "207,308,414,500,604,726,810,891,982,1075,1170,1264,1364,1457,1552,1657,1748,1839,1925,2030,2136,2239,2345,2454,2561,2731,2828,2915"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,178", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,815,896,987,1080,1175,1269,1369,1462,1557,1662,1753,1844,1930,2035,2141,2244,2350,2459,2566,2736,18235", "endColumns": "106,100,105,85,103,121,83,80,90,92,94,93,99,92,94,104,90,90,85,104,105,102,105,108,106,169,96,86", "endOffsets": "207,308,414,500,604,726,810,891,982,1075,1170,1264,1364,1457,1552,1657,1748,1839,1925,2030,2136,2239,2345,2454,2561,2731,2828,18317"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\b12f5ec7860c79fe319ea01d980e4aac\\transformed\\ui-release\\res\\values-sr\\values-sr.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "194,291,378,475,576,662,739,830,922,1007,1087,1172,1245,1334,1411,1489,1565,1644,1714", "endColumns": "96,86,96,100,85,76,90,91,84,79,84,72,88,76,77,75,78,69,117", "endOffsets": "286,373,470,571,657,734,825,917,1002,1082,1167,1240,1329,1406,1484,1560,1639,1709,1827"}, "to": {"startLines": "37,38,40,42,43,72,73,172,173,176,177,179,180,181,182,183,186,187,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3647,3744,3902,4076,4177,6700,6777,17661,17753,18070,18150,18322,18395,18484,18561,18639,18985,19064,19134", "endColumns": "96,86,96,100,85,76,90,91,84,79,84,72,88,76,77,75,78,69,117", "endOffsets": "3739,3826,3994,4172,4258,6772,6863,17748,17833,18145,18230,18390,18479,18556,18634,18710,19059,19129,19247"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\114a5d5301d7649ab21683cbabeecc89\\transformed\\media3-exoplayer-1.7.1\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,190,255,326,404,476,563,646", "endColumns": "73,60,64,70,77,71,86,82,72", "endOffsets": "124,185,250,321,399,471,558,641,714"}, "to": {"startLines": "61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "5822,5896,5957,6022,6093,6171,6243,6330,6413", "endColumns": "73,60,64,70,77,71,86,82,72", "endOffsets": "5891,5952,6017,6088,6166,6238,6325,6408,6481"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\493af54b1b0004569d1870cf1ba0af14\\transformed\\media3-session-1.7.1\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,244,319,412,503,601,696,785,891,980,1045,1150,1241,1335,1414,1510,1595,1691,1762,1829,1911,1997,2093", "endColumns": "76,111,74,92,90,97,94,88,105,88,64,104,90,93,78,95,84,95,70,66,81,85,95,101", "endOffsets": "127,239,314,407,498,596,691,780,886,975,1040,1145,1236,1330,1409,1505,1590,1686,1757,1824,1906,1992,2088,2190"}, "to": {"startLines": "41,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,132,133,134,135,136,137", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3999,4263,4375,4450,4543,4634,4732,4827,4916,5022,5111,5176,5281,5372,5466,5545,5641,5726,13230,13301,13368,13450,13536,13632", "endColumns": "76,111,74,92,90,97,94,88,105,88,64,104,90,93,78,95,84,95,70,66,81,85,95,101", "endOffsets": "4071,4370,4445,4538,4629,4727,4822,4911,5017,5106,5171,5276,5367,5461,5540,5636,5721,5817,13296,13363,13445,13531,13627,13729"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\8.11.1\\transforms\\c3709e4bfd5d4e5c1ed8a624d490cd12\\transformed\\material-release\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "92", "endOffsets": "143"}, "to": {"startLines": "131", "startColumns": "4", "startOffsets": "13137", "endColumns": "92", "endOffsets": "13225"}}]}]}