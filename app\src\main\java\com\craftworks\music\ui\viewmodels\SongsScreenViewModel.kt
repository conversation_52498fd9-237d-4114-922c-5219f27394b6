package com.craftworks.music.ui.viewmodels

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.media3.common.MediaItem
import com.craftworks.music.providers.getSongs
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

class SongsScreenViewModel : ViewModel(), ReloadableViewModel {
    private val _allSongs = MutableStateFlow<List<MediaItem>>(emptyList())
    val allSongs: StateFlow<List<MediaItem>> = _allSongs.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private var hasLoadedData = false

    fun loadDataIfNeeded() {
        if (!hasLoadedData && !_isLoading.value) {
            reloadData()
        }
    }

    override fun reloadData() {
        if (_isLoading.value) return

        viewModelScope.launch {
            _isLoading.value = true
            try {
                Log.d("SONGS_VM", "Loading songs with performance optimization...")

                val songs = try {
                    getSongs() // Load songs with existing optimizations
                } catch (e: Exception) {
                    Log.e("SONGS_VM", "Failed to load songs: ${e.message}", e)
                    emptyList()
                }

                _allSongs.value = songs
                hasLoadedData = true
                Log.d("SONGS_VM", "Loaded ${songs.size} songs")
            } catch (e: Exception) {
                Log.e("SONGS_VM", "Error in reloadData: ${e.message}", e)
            } finally {
                _isLoading.value = false
            }
        }
    }
    fun getMoreSongs(size: Int){
        viewModelScope.launch {
            val songOffset = _allSongs.value.size
            _allSongs.value += getSongs(songCount = size, songOffset = songOffset)
        }
    }

    suspend fun search(query: String){
        _allSongs.value = getSongs(query, 500)
    }
}