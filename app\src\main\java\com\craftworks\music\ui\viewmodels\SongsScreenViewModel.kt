package com.craftworks.music.ui.viewmodels

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.media3.common.MediaItem
import com.craftworks.music.providers.getSongs
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

class SongsScreenViewModel : ViewModel(), ReloadableViewModel {
    private val _allSongs = MutableStateFlow<List<MediaItem>>(emptyList())
    val allSongs: StateFlow<List<MediaItem>> = _allSongs.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private var hasLoadedData = false

    fun loadDataIfNeeded() {
        if (!hasLoadedData && !_isLoading.value) {
            reloadData()
        }
    }

    override fun reloadData() {
        if (_isLoading.value) return

        viewModelScope.launch {
            _isLoading.value = true
            try {
                coroutineScope {
                    _allSongs.value = getSongs()
                }
                hasLoadedData = true
            } finally {
                _isLoading.value = false
            }
        }
    }
    fun getMoreSongs(size: Int){
        viewModelScope.launch {
            val songOffset = _allSongs.value.size
            _allSongs.value += getSongs(songCount = size, songOffset = songOffset)
        }
    }

    suspend fun search(query: String){
        _allSongs.value = getSongs(query, 500)
    }
}